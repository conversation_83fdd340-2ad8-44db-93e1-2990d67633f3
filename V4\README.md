# DraconiaSaga Daily Login Bot V4

A comprehensive automated script for DraconiaSaga daily login tasks with multi-account and multi-character support.

## Features

### Core Functionality
- **Multi-Account Support**: Manage multiple game accounts with different credentials
- **Multi-Character Support**: Handle multiple characters per account automatically
- **Dual Monitor Support**: Works on both main (1360x768) and secondary (1920x1080) monitors
- **Smart Window Detection**: Automatically finds and focuses on DraconiaSaga game window
- **Human-like Behavior**: Random delays between actions to avoid detection

### GUI Features
- **Modern Interface**: Clean, scrollable GUI with organized sections
- **Real-time Timer**: Shows how long the bot has been running
- **Activity Log**: Detailed logging of all bot actions
- **Hotkey Support**: F1-F5 hotkeys for quick control
- **Step Selection**: Choose which step to start from (1-7)
- **Monitor Selection**: Choose between main and secondary monitor

### Automation Steps
1. **Login**: Email/password entry, server selection, character selection
2. **Post-Login**: Close ads, treasure hunter retrieval, inventory management
3. **Functions**: Dragon Hunters and Brigade rewards
4. **Recharge**: Value packs, daily packs, free gifts
5. **Benefits**: Sign-in gifts, golden egg, daily discount, PC login rewards
6. **Summon**: Free summons across all available tabs
7. **Logout**: Character/account switching for continuous operation

### Advanced Features
- **Event Scripts**: Import custom Python scripts for special events
- **Screenshot Management**: Built-in snipping tool for easy screenshot capture
- **Missing Screenshot Detection**: Lists all required screenshots not yet captured
- **Smart Recovery**: Manual intervention support with continuation capability
- **Configuration Persistence**: Saves settings and account information

## Installation

### Prerequisites
- Windows 10/11
- Python 3.8 or higher
- DraconiaSaga game installed

### Quick Setup
1. Download/extract the V4 folder
2. Run `install.bat` as administrator
3. Wait for all dependencies to install
4. Run `run.bat` to start the bot

### Manual Installation
```bash
pip install pyautogui==0.9.54
pip install opencv-python==********
pip install numpy==1.24.3
pip install Pillow==10.0.1
pip install keyboard==0.13.5
pip install pywin32==306
```

## Setup Guide

### 1. Account Configuration
- Click "Add Account" to add your game accounts
- Enter email, password, server (e.g., S98), and number of characters
- Default accounts are provided as examples

### 2. Monitor Setup
- Select your monitor resolution:
  - Main Monitor: 1360x768 (100% scale)
  - Secondary Monitor: 1920x1080 (150% scale)
- Screenshots will be saved to the appropriate folder

### 3. Screenshot Capture
- Click "Take Screenshot" to capture game elements
- Use the snipping tool interface to select specific areas
- Click "Missing Screenshots" to see what's still needed
- Screenshots are saved to `screenshots/main_1360x768/` or `screenshots/secondary_1920x1080/`

### Required Screenshots
The bot needs these screenshots for image recognition:
- Login elements: `email_field.png`, `password_field.png`, `login_button.png`
- Server selection: `server_arrow.png`, `server_selection.png`
- Characters: `character_1.png`, `character_2.png`, `character_3.png`, `character_4.png`
- UI elements: `start_button.png`, `close_ads_x.png`, `inventory_button.png`
- Functions: `functions_button.png`, `dragon_hunters.png`, `brigade.png`
- And many more (see "Missing Screenshots" for complete list)

## Usage

### Basic Operation
1. Start DraconiaSaga game
2. Run the bot using `run.bat` or `python DailyLogin.py`
3. Configure accounts and monitor settings
4. Click "Start (F1)" to begin automation
5. Monitor progress in the activity log

### Hotkeys
- **F1**: Start bot
- **F2**: Pause/Resume bot
- **F3**: Skip to next step
- **F4**: Repeat current step
- **F5**: Stop bot

### Step Selection
- Choose starting step (1-7) if you want to skip certain parts
- Useful if you've already completed some steps manually

### Manual Intervention
- If a step fails, you can manually complete it
- Use "Next (F3)" to continue from the next step
- The bot will resume normal operation

## Event Scripts

### Adding Custom Scripts
1. Create a Python file with your custom automation
2. Include an `execute_event(bot)` function
3. Use "Add Event Script" to import it
4. Scripts run automatically between Step 2 and Step 3

### Example Event Script
```python
def execute_event(bot):
    """Custom event script example"""
    bot.log("Executing custom event script")
    
    # Your custom automation code here
    # You can use bot.find_and_click() and other bot methods
    
    if bot.find_and_click("special_event_button.png"):
        bot.log("Special event completed")
    
    bot.human_delay()
```

## Configuration

### Settings File
- Configuration is automatically saved to `config.json`
- Includes account information, monitor settings, and preferences
- Can be manually edited if needed

### Delay Settings
- Default delays: 1.0-3.0 seconds between actions
- Randomized to simulate human behavior
- Configurable in the code if needed

## Troubleshooting

### Common Issues

**Bot can't find game window**
- Make sure DraconiaSaga is running
- Check window title contains "draconiasaga" or "dragonsaga"

**Screenshots not working**
- Ensure you're using the correct monitor setting
- Take screenshots while game is visible and active
- Use the built-in snipping tool for precise capture

**Hotkeys not working**
- Run as administrator if needed
- Check if other applications are using the same hotkeys

**Steps failing**
- Check if required screenshots exist
- Verify game UI hasn't changed
- Use manual intervention to continue

### Debug Mode
- Check the activity log for detailed error messages
- Screenshots are logged when found/not found
- Step execution results are clearly indicated

## Advanced Configuration

### Monitor Scaling
- Main monitor: 1360x768 @ 100% scale
- Secondary monitor: 1920x1080 @ 150% scale
- Screenshots are resolution-specific

### Character Positions
- Fixed fallback positions for character selection
- Position 1: (400, 300), Position 2: (600, 300)
- Position 3: (400, 450), Position 4: (600, 450)

### Weekly Features
- Weekly pack claiming (Mondays only)
- PC login rewards (14-day cycle)
- Automatic date-based feature activation

## Safety Features

### Human-like Behavior
- Random delays between 1-3 seconds
- Variable typing speed
- Natural mouse movement patterns

### Error Recovery
- Automatic retry on failed steps
- Manual intervention support
- Graceful error handling

### Window Management
- Only operates on active DraconiaSaga window
- Automatic window focusing
- Multi-monitor awareness

## Support

### Getting Help
- Check the activity log for error details
- Verify all required screenshots are captured
- Ensure game UI matches expected layout
- Test individual steps using step selection

### Updates
- V4 includes all features from previous versions
- Enhanced GUI and user experience
- Improved error handling and recovery
- Better multi-monitor support

## Version History

### V4 Features
- Complete GUI redesign with scrollable interface
- Built-in screenshot management
- Event script system
- Enhanced multi-monitor support
- Improved error handling
- Real-time activity logging
- Hotkey support
- Step selection capability

### Previous Versions
- V3: Basic automation with tabbed interface
- V2: Multi-account support
- V1: Single account automation

## License

This software is provided as-is for educational and personal use only. Use responsibly and in accordance with game terms of service.
