"""
Example Event Script for DraconiaSaga Daily Login Bot V4

This is an example of how to create custom event scripts that can be imported
into the bot for special events, seasonal activities, or custom automation.

To use this script:
1. Modify the execute_event function below with your custom automation
2. Add this script using the "Add Event Script" button in the bot GUI
3. The script will automatically run between Step 2 and Step 3

The bot object provides access to all bot methods including:
- bot.find_and_click(image_name) - Find and click on an image
- bot.log(message) - Add message to the activity log
- bot.human_delay() - Add random human-like delay
- bot.type_text(text) - Type text with human-like delays
- bot.is_paused - Check if bot is paused
- bot.config - Access bot configuration
"""

import time
import random

def execute_event(bot):
    """
    Main event function that gets called by the bot
    
    Args:
        bot: The DraconiaSagaBot instance with access to all bot methods
    """
    bot.log("Starting example event script")
    
    # Example 1: Simple button clicking
    # Replace "special_event_button.png" with your actual screenshot
    if bot.find_and_click("special_event_button.png", timeout=5):
        bot.log("Found and clicked special event button")
        bot.human_delay()
        
        # Click claim or collect button
        if bot.find_and_click("claim_reward.png", timeout=3):
            bot.log("Claimed event reward")
        
        # Close event window
        bot.find_and_click("close_ads_x.png", timeout=2)
    else:
        bot.log("Special event button not found - event may not be active")
    
    # Example 2: Multiple attempts with different images
    event_buttons = [
        "seasonal_event_1.png",
        "seasonal_event_2.png", 
        "limited_time_event.png"
    ]
    
    for button in event_buttons:
        if bot.find_and_click(button, timeout=2):
            bot.log(f"Found event: {button}")
            
            # Perform event-specific actions
            perform_event_actions(bot)
            break
    
    # Example 3: Conditional logic based on day of week
    import datetime
    today = datetime.datetime.now().weekday()
    
    if today == 0:  # Monday
        bot.log("Monday special event check")
        if bot.find_and_click("monday_special.png", timeout=3):
            bot.log("Completed Monday special event")
    elif today == 5:  # Saturday
        bot.log("Weekend event check")
        if bot.find_and_click("weekend_event.png", timeout=3):
            bot.log("Completed weekend event")
    
    # Example 4: Multiple clicks with verification
    if bot.find_and_click("daily_spin.png", timeout=5):
        bot.log("Found daily spin wheel")
        
        # Spin multiple times if available
        for i in range(3):
            if bot.find_and_click("spin_button.png", timeout=2):
                bot.log(f"Spin {i+1} completed")
                time.sleep(2)  # Wait for spin animation
                
                # Click anywhere to continue
                import pyautogui
                pyautogui.click(500, 400)
                bot.human_delay()
            else:
                bot.log(f"No more spins available after {i} spins")
                break
        
        # Close spin window
        bot.find_and_click("close_ads_x.png")
    
    bot.log("Example event script completed")

def perform_event_actions(bot):
    """
    Helper function for common event actions
    """
    # Common event sequence: claim -> confirm -> close
    actions = [
        ("claim_now.png", "Claiming reward"),
        ("confirm_button.png", "Confirming action"),
        ("collect_all.png", "Collecting all rewards"),
        ("close_ads_x.png", "Closing window")
    ]
    
    for image, message in actions:
        if bot.find_and_click(image, timeout=2):
            bot.log(message)
            bot.human_delay()

def check_event_availability(bot):
    """
    Helper function to check if events are available
    Returns True if any event is found
    """
    event_indicators = [
        "event_notification.png",
        "red_dot_indicator.png",
        "new_event_badge.png"
    ]
    
    for indicator in event_indicators:
        try:
            import pyautogui
            folder = bot.get_monitor_screenshot_folder()
            import os
            image_path = os.path.join(folder, indicator)
            
            if os.path.exists(image_path):
                location = pyautogui.locateOnScreen(image_path, confidence=0.8)
                if location:
                    bot.log(f"Event indicator found: {indicator}")
                    return True
        except:
            pass
    
    return False

# Additional helper functions can be added here
def handle_special_popup(bot):
    """Handle special popups that might appear during events"""
    popups = [
        "maintenance_notice.png",
        "update_available.png",
        "connection_error.png"
    ]
    
    for popup in popups:
        if bot.find_and_click(popup, timeout=1):
            bot.log(f"Handled popup: {popup}")
            # Click OK or Close
            bot.find_and_click("ok_button.png", timeout=2)
            bot.find_and_click("close_ads_x.png", timeout=2)
            return True
    
    return False

# You can add more custom functions as needed for your specific events
