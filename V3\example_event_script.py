"""
Example Event Script for DraconiaSaga Bot
This script demonstrates how to create additional functionality that can be loaded into the bot.

To use this script:
1. Load it through the Events tab in the bot GUI
2. Schedule it to run at specific times/days
3. The bot will automatically execute this script when conditions are met

The script must have an 'execute' function that takes the bot instance as parameter.
"""

import time
import random
from datetime import datetime


def execute(bot):
    """
    Main execution function called by the bot
    
    Args:
        bot: The DraconiaSagaBot instance with access to all bot methods
    """
    bot.log("Starting example event script execution")
    
    # Example: Check if it's a specific day for special events
    current_day = datetime.now().strftime("%A")
    
    if current_day in ["Saturday", "Sunday"]:
        bot.log("Weekend detected - running weekend event routine")
        weekend_routine(bot)
    else:
        bot.log("Weekday detected - running daily event routine")
        daily_routine(bot)
    
    bot.log("Example event script completed")


def weekend_routine(bot):
    """Weekend-specific event routine"""
    steps = [
        "Check weekend events",
        "Participate in guild activities",
        "Complete weekend quests",
        "Collect weekend rewards"
    ]
    
    for i, step in enumerate(steps):
        if not bot.is_running or bot.is_paused:
            break
            
        bot.log(f"Weekend step {i+1}: {step}")
        
        # Here you would implement actual image recognition and clicking
        # For example:
        # if bot.click_image(f"images/weekend_{i+1}.png"):
        #     bot.log(f"Successfully completed: {step}")
        # else:
        #     bot.log(f"Failed to find image for: {step}")
        
        # Add random delay to appear more human-like
        bot.random_delay()


def daily_routine(bot):
    """Daily event routine"""
    steps = [
        "Check daily events",
        "Complete event quests",
        "Collect event rewards",
        "Check event shop"
    ]
    
    for i, step in enumerate(steps):
        if not bot.is_running or bot.is_paused:
            break
            
        bot.log(f"Daily event step {i+1}: {step}")
        
        # Here you would implement actual image recognition and clicking
        # Example implementation:
        # if bot.click_image(f"images/daily_event_{i+1}.png"):
        #     bot.log(f"Successfully completed: {step}")
        # else:
        #     bot.log(f"Failed to find image for: {step}")
        
        # Add random delay
        bot.random_delay()


def check_special_conditions(bot):
    """
    Check for special conditions that might affect event execution
    This function can be called from the main execute function
    """
    # Example: Check current time
    current_hour = datetime.now().hour
    
    if 2 <= current_hour <= 6:
        bot.log("Late night hours detected - reducing activity")
        return "night_mode"
    elif 12 <= current_hour <= 14:
        bot.log("Lunch time detected - normal activity")
        return "lunch_time"
    else:
        return "normal"


# Additional utility functions can be added here
def custom_image_search(bot, image_name, timeout=10):
    """
    Custom image search with timeout
    
    Args:
        bot: Bot instance
        image_name: Name of image file to search for
        timeout: Maximum time to search in seconds
    
    Returns:
        bool: True if image found and clicked, False otherwise
    """
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        if not bot.is_running or bot.is_paused:
            return False
            
        if bot.click_image(f"images/{image_name}"):
            return True
            
        time.sleep(0.5)
    
    bot.log(f"Timeout reached searching for {image_name}")
    return False
