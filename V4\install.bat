@echo off
echo Installing DraconiaSaga Daily Login Bot V4 Dependencies...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Installing required packages...
echo.

REM Upgrade pip first
python -m pip install --upgrade pip

REM Uninstall potentially conflicting packages first
python -m pip uninstall -y numpy opencv-python opencv-contrib-python

REM Install numpy first (required for OpenCV)
python -m pip install numpy==1.26.4

REM Install other packages
python -m pip install opencv-python==*********
python -m pip install pyautogui==0.9.54
python -m pip install Pillow==10.4.0
python -m pip install keyboard==0.13.5
python -m pip install pywin32==306

echo.
echo Installation completed!
echo.
echo You can now run the bot using: python DailyLogin.py
echo Or use the run.bat file for convenience.
echo.
pause
