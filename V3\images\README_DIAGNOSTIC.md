# Image Recognition Setup Guide

This guide explains what reference images you need to capture for the DraconiaSaga Daily Login Bot to work properly.

## How to Capture Images

1. **Open DraconiaSaga** and navigate to the screen you want to capture
2. **Run the bot** and use the "Take Screenshot" button in the Main Control tab
3. **Crop the screenshot** to show only the specific UI element (button, field, etc.)
4. **Save with the exact filename** listed below in the `images/` folder
5. **Test recognition** by running the bot and checking the logs

## Required Image Files

### PART 1: LOGIN SCREEN

#### Login Form
- **`email_field.png`** - The email/username input field (empty field or with placeholder text)
- **`password_field.png`** - The password input field (empty field or with dots)
- **`login_button.png`** - The "Login" button

#### Server Selection
- **`server_s98.png`** - The S98 server option/button
- **`server_selection.png`** - General server selection area (fallback)

#### Character Selection
- **`character_1.png`** - First character slot (with or without character)
- **`character_2.png`** - Second character slot
- **`character_3.png`** - Third character slot (if needed)
- **`character_4.png`** - Fourth character slot (if needed)
- **`start_button.png`** - The "Start" button after character selection

### PART 2: AFTER LOGIN

#### Advertisement Handling
- **`ad_close_x.png`** - The X button to close advertisement popups

#### Treasure Hunter (Optional Popup)
- **`treasure_hunter_popup.png`** - The treasure hunter rewards popup window
- **`perfect_retrieval.png`** - The "Perfect Retrieval" option with diamond logo
- **`quick_normal_retrieval.png`** - The "Quick Normal Retrieval" button
- **`treasure_hunter_close.png`** - X button to close treasure hunter window

#### Inventory Management
- **`inventory_button.png`** - The inventory button on main UI (right side)
- **`put_into_furnace.png`** - The "Put into the furnace" button
- **`all_quality_checkbox.png`** - The "All Quality" checkbox (unchecked state)
- **`put_button.png`** - The "Put" button
- **`confirm_button.png`** - The "Confirm" button (used in multiple places)
- **`inventory_close_x.png`** - X button to close inventory window

### STEP 3: FUNCTIONS

#### Main Functions
- **`functions_button.png`** - The "Functions" button on main UI

#### Dragon Hunters
- **`dragon_hunters.png`** - The "Dragon Hunters" option in functions menu
- **`claim_now.png`** - The "Claim Now" button (generic, used in multiple places)
- **`use_button.png`** - The "USE" button that appears in popups
- **`dragon_hunters_close.png`** - X button to close Dragon Hunters window

#### Brigade
- **`brigade.png`** - The "Brigade" option in functions menu
- **`brigade_claim_now.png`** - The "Claim Now" button specifically for Brigade
- **`brigade_support.png`** - The "Brigade Support" button
- **`brigade_support_green.png`** - The green "Brigade Support" button (second click)
- **`brigade_close.png`** - X button to close Brigade window

### STEP 4: RECHARGE

#### Main Recharge
- **`recharge_button.png`** - The "Recharge" button on main UI (top right area)

#### Recharge Options
- **`value_pack.png`** - The "Value Pack" option
- **`value_pack_claim.png`** - Claim button for Value Pack
- **`daily_pack.png`** - The "Daily Pack" option
- **`daily_pack_claim.png`** - Claim button for Daily Pack
- **`daily_free_gift.png`** - The "Daily Free Gift" section
- **`daily_free_gift_claim.png`** - Claim button for Daily Free Gift
- **`weekly_pack.png`** - The "Weekly Pack" option (Monday only)
- **`weekly_pack_claim.png`** - Claim button for Weekly Pack

### STEP 5: BENEFITS

#### Main Benefits
- **`benefits_button.png`** - The "Benefits" button on main UI (left of Recharge)
- **`benefits_close.png`** - X button to close Benefits window

#### Sign-in Gift
- **`signin_daily.png`** - The daily sign-in gift (blinking/available state)
- **`signin_10day.png`** - The 10-day bonus button (purple)
- **`signin_20day.png`** - The 20-day bonus button (orange)

#### Golden Egg Daily
- **`golden_egg_smash.png`** - The "Smash the Golden Egg Free" button

#### Daily Discount
- **`daily_discount_claim.png`** - The "Claim now" button for Daily Discount

#### PC Login
- **`pc_login_section.png`** - The PC Login section (after scrolling down)
- **`pc_login_day_1.png`** through **`pc_login_day_14.png`** - Individual day rewards (light up state)
- **`pc_login_box.png`** - The special box reward (rabbit holding box)

### STEP 6: SUMMON

#### Main Summon
- **`summon_button.png`** - The "Summon" button on main UI
- **`summon_close.png`** - X button to close Summon window
- **`drakite_rewards_close.png`** - X button to close Drakite Rewards popup
- **`skip_all.png`** - The "Skip All" button for summon animations

#### Summon Tabs
- **`summon_supreme_monarch.png`** - Supreme Monarch tab
- **`summon_violet_mirage.png`** - Violet Mirage tab
- **`summon_lumin_feather.png`** - Lumin Feather tab
- **`summon_glistening_shadow.png`** - Glistening Shadow tab
- **`summon_tainted_woods.png`** - Tainted Woods tab
- **`summon_snowy_bloom.png`** - Snowy Bloom tab
- **`summon_holy_summon.png`** - Holy Summon tab
- **`summon_summon_dragons.png`** - Summon Dragons tab

#### Free Summon Buttons (for each tab)
- **`supreme_monarch_free_summon.png`** - "Summon x 1 Free" for Supreme Monarch
- **`violet_mirage_free_summon.png`** - "Summon x 1 Free" for Violet Mirage
- **`lumin_feather_free_summon.png`** - "Summon x 1 Free" for Lumin Feather
- **`glistening_shadow_free_summon.png`** - "Summon x 1 Free" for Glistening Shadow
- **`tainted_woods_free_summon.png`** - "Summon x 1 Free" for Tainted Woods
- **`snowy_bloom_free_summon.png`** - "Summon x 1 Free" for Snowy Bloom
- **`holy_summon_free_summon.png`** - "Summon x 1 Free" for Holy Summon
- **`summon_dragons_free_summon.png`** - "Summon x 1 Free" for Summon Dragons

### STEP 7: LOGOUT

#### Settings Access
- **`settings_gear.png`** - The gear/settings icon at bottom of Functions menu
- **`account_button.png`** - The "Account" button in settings

#### Character/Account Change
- **`change_character.png`** - The "Change Character" option
- **`change_account.png`** - The "Change Account" option
- **`switch_account.png`** - The "Switch Account" button
- **`notice_close.png`** - X button to close notice popup

## Image Capture Tips

### Best Practices
1. **Capture at native resolution** - Don't resize or compress images
2. **Include minimal surrounding area** - Just the button/field, not the whole screen
3. **Capture in different states** - Some buttons change appearance when available/unavailable
4. **Use consistent naming** - Follow the exact filenames listed above
5. **Test immediately** - Run the bot after capturing each image to verify recognition

### Common Issues
1. **Image too large** - Crop to show only the specific UI element
2. **Image too small** - Include enough detail for recognition
3. **Wrong state** - Capture buttons in their active/clickable state
4. **Compression artifacts** - Save as PNG without compression
5. **Monitor scaling** - Ensure images match your selected monitor's scaling

### Recognition Testing
1. **Start the bot** with your captured images
2. **Watch the logs** for "Image found" vs "Image not found" messages
3. **Adjust confidence threshold** in Settings if needed (0.6-0.9)
4. **Recapture problematic images** with different cropping or timing

## Priority Order

If you can't capture all images at once, prioritize in this order:

### Essential (Bot won't work without these)
1. Login screen images (email_field, password_field, login_button)
2. Character selection (character_1, start_button)
3. Main UI buttons (inventory_button, functions_button, etc.)

### Important (Core functionality)
1. Functions menu items (dragon_hunters, brigade, claim_now)
2. Recharge items (value_pack, daily_pack)
3. Benefits items (signin_daily, golden_egg_smash)

### Optional (Enhanced functionality)
1. Treasure hunter popup handling
2. All summon tabs and free summon buttons
3. PC Login individual day rewards
4. Weekly pack (Monday only)

## Troubleshooting

### Image Not Found
1. **Check filename** - Must match exactly (case-sensitive)
2. **Verify image quality** - Clear, uncompressed PNG
3. **Adjust confidence** - Lower threshold (0.6-0.7) for difficult images
4. **Recapture** - Different timing or game state
5. **Check logs** - Look for specific error messages

### False Positives
1. **Crop more precisely** - Remove surrounding elements
2. **Increase confidence** - Higher threshold (0.8-0.9)
3. **Capture unique elements** - Avoid generic buttons

### Monitor Issues
1. **Select correct monitor** - Main vs Secondary in GUI
2. **Check scaling** - 100% vs 150% affects image recognition
3. **Consistent capture** - Always capture on the same monitor you'll run on

Remember: The bot will gracefully handle missing images by logging warnings and continuing to the next step. Start with essential images and add more over time for full functionality.
