@echo off
echo Starting DraconiaSaga Daily Login Bot V4...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    echo Or run install.bat first to set up dependencies
    pause
    exit /b 1
)

REM Check if DailyLogin.py exists
if not exist "DailyLogin.py" (
    echo ERROR: DailyLogin.py not found in current directory
    echo Please make sure you are running this from the V4 folder
    pause
    exit /b 1
)

echo Starting the bot...
echo.
echo Press Ctrl+C to stop the bot
echo.

REM Run the bot
python DailyLogin.py

echo.
echo Bot has stopped.
pause
