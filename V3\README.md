# DraconiaSaga Daily Login Bot

An automated Python script for DraconiaSaga game that handles daily login tasks, reward collection, and multi-account/character management.

## Features

- **Complete 7-Step Automation** following the game's daily routine
- **Multi-Account Support** with automatic switching
- **Multi-Character Support** per account
- **Dual Monitor Support** (1360x768 @ 100% and 1920x1080 @ 150%)
- **Human-like Behavior** with random delays
- **GUI Interface** with real-time logging
- **Hotkey Controls** (F1-F5)
- **Event Script System** for additional functionality
- **Screenshot Tools** for image recognition setup
- **Configuration Management** with JSON storage

## Installation

### Python Version Compatibility

**Recommended**: Python 3.11 or 3.12 for best compatibility

**Python 3.13.5 Users**: Some packages (especially Pillow) may not have pre-compiled wheels available. See troubleshooting section below.

### Installation Steps

1. **Install Python 3.8+** if not already installed
2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
   Or run the provided batch file:
   ```bash
   install.bat
   ```

### Python 3.13.5 Specific Instructions

If you encounter "wheel cannot be found" errors with Python 3.13.5:

**Option 1 (Recommended)**: Use Python 3.11 or 3.12
- Download from [python.org](https://python.org)
- These versions have better package compatibility

**Option 2**: Install with compilation support
```bash
# Upgrade pip first
python -m pip install --upgrade pip

# Install packages individually
pip install keyboard==0.13.5
pip install pygetwindow==0.0.9
pip install pyautogui==0.9.54
pip install --upgrade Pillow
```

**Option 3**: Use Conda instead of pip
```bash
conda install pillow pyautogui
pip install keyboard pygetwindow
```

## Quick Start

1. **Run the Bot**:
   ```bash
   python DailyLogin.py
   ```
   Or use the batch file:
   ```bash
   run.bat
   ```

2. **Add Your Accounts**:
   - Go to the "Accounts" tab
   - Click "Add Account"
   - Enter your email, password, and character names (one per line)
   - Example accounts from PDF:
     - `<EMAIL>` / `Abc123` / 2 characters
     - `<EMAIL>` / `Abc123` / 1 character  
     - `<EMAIL>` / `Xyz789` / 1 character

3. **Configure Settings**:
   - Go to "Settings" tab
   - Adjust delay ranges (default: 1-3 seconds)
   - Set image recognition confidence (default: 0.8)

4. **Start Automation**:
   - Press F1 or click "Start"
   - The bot will automatically cycle through all accounts and characters

## Automation Steps

The bot follows the exact 7-step process from the specification:

### PART 1: LOGIN
- Enters email and password
- Selects Server S98
- Chooses the appropriate character
- Clicks Start

### PART 2: AFTER SUCCESSFUL LOGIN
- Closes advertisement popups (X button)
- Handles Treasure Hunter rewards if available
- Opens inventory and puts items into furnace
- Selects "All Quality" and confirms

### STEP 3: FUNCTIONS
- **Dragon Hunters**: Claims rewards and handles USE button popups
- **Brigade**: Claims rewards and clicks Brigade Support options

### STEP 4: RECHARGE
- **Value Pack**: Claims if available
- **Daily Pack**: Claims if available  
- **Daily Free Gift**: Claims if available
- **Weekly Pack**: Claims on Mondays only

### STEP 5: BENEFITS
- **Sign-in Gift**: Daily, 10-day (purple), 20-day (orange) bonuses
- **Golden Egg Daily**: Smashes up to 3 times
- **Daily Discount**: Claims if available
- **PC Login**: Claims up to 14 days of rewards + special boxes

### STEP 6: SUMMON
- Checks all summon tabs for free summons:
  - Supreme Monarch, Violet Mirage, Lumin Feather
  - Glistening Shadow, Tainted Woods, Snowy Bloom
  - Holy Summon, Summon Dragons
- Uses "Skip All" for summoning animations

### STEP 7: LOGOUT
- Changes to next character (if available)
- Or switches to next account
- Cycles back to first account when all are complete

## Controls

### Hotkeys (Global)
- **F1**: Start automation
- **F2**: Pause/Resume
- **F3**: Force next step
- **F4**: Repeat current step
- **F5**: Stop automation

### GUI Controls
- **Start/Pause/Stop**: Control automation
- **Monitor Selection**: Choose which monitor to run on
- **Screenshot Tools**: Capture images for recognition
- **Account Management**: Add/edit/remove accounts
- **Event Scripts**: Load additional automation scripts
- **Settings**: Configure delays and recognition threshold

## Image Recognition Setup

The bot uses image recognition to interact with the game. You need to capture reference images:

1. **Take Screenshots**: Use the "Take Screenshot" button while the game is open
2. **Save Reference Images**: Place them in the `images/` folder with specific names:

### Required Image Files
```
images/
├── email_field.png          # Login email input field
├── password_field.png       # Login password input field  
├── login_button.png         # Login button
├── server_s98.png          # S98 server selection
├── character_1.png         # Character slot 1
├── character_2.png         # Character slot 2
├── start_button.png        # Character selection start button
├── ad_close_x.png          # Advertisement close X button
├── inventory_button.png    # Main UI inventory button
├── functions_button.png    # Main UI functions button
├── recharge_button.png     # Main UI recharge button
├── benefits_button.png     # Main UI benefits button
├── summon_button.png       # Main UI summon button
└── ... (and many more)
```

3. **Test Recognition**: Use the screenshot tools to verify images are detected correctly

## Multi-Monitor Support

The bot supports dual monitor setups:

- **Main Monitor**: 1360x768 @ 100% scale
- **Secondary Monitor**: 1920x1080 @ 150% scale

Select the appropriate monitor in the GUI before starting automation.

## Event Scripts

Add custom functionality with event scripts:

1. **Create Script**: Follow the example in `example_event_script.py`
2. **Load Script**: Use the Events tab to load your script
3. **Schedule**: Set when the script should run (daily/weekly/seasonal)

Example event script structure:
```python
def execute(bot):
    """Main function called by the bot"""
    bot.log("Custom event script running")
    # Your custom automation here
```

## Configuration

Settings are automatically saved to `draconia_config.json`:

```json
{
  "accounts": [
    {
      "username": "<EMAIL>",
      "password": "Abc123",
      "characters": ["Character1", "Character2"],
      "status": "Ready",
      "last_login": "Never"
    }
  ],
  "settings": {
    "min_delay": 1.0,
    "max_delay": 3.0,
    "confidence": 0.8
  }
}
```

## Troubleshooting

### Common Issues

1. **Game Window Not Found**:
   - Ensure DraconiaSaga is running
   - Check window title matches expected names
   - Try different monitor selection

2. **Image Recognition Fails**:
   - Recapture reference images
   - Adjust confidence threshold (0.6-0.9)
   - Ensure game resolution matches monitor settings

3. **Bot Gets Stuck**:
   - Use F4 to repeat current step
   - Use F3 to force next step
   - Check logs for specific errors

4. **Hotkeys Not Working**:
   - Run as administrator
   - Check for conflicting software
   - Restart the application

### Debug Mode

Enable detailed logging by checking the console output and log files. The bot provides extensive diagnostic information for troubleshooting.

## Safety Features

- **Human-like Delays**: Random delays between 1-3 seconds (configurable)
- **Window Detection**: Only runs when DraconiaSaga is active
- **Error Handling**: Graceful failure recovery
- **Manual Override**: Hotkeys for manual control
- **Pause/Resume**: Safe interruption of automation

## Legal Notice

This bot is for educational purposes. Use responsibly and in accordance with the game's terms of service. The authors are not responsible for any consequences of using this software.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review the logs for error messages
3. Ensure all reference images are properly captured
4. Verify account credentials and character names

## Version History

- **v1.0**: Initial release with complete 7-step automation
- Full PDF specification implementation
- Multi-account and multi-character support
- Dual monitor compatibility
- Event script system
