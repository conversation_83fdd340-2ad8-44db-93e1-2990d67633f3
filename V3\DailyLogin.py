import importlib.util
import json
import os
import random
import threading
import time
import tkinter as tk
from datetime import datetime
from tkinter import filedialog, messagebox, scrolledtext, ttk

import keyboard
import pyautogui
import pygetwindow as gw


class DraconiaSagaBot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DraconiaSaga Daily Login Bot")
        self.root.geometry("800x600")

        # Bot state variables
        self.is_running = False
        self.is_paused = False
        self.current_step = 0
        self.accounts = []
        self.current_account = 0
        self.current_character = 0
        self.timer_start = None
        self.selected_monitor = 0  # 0 for main, 1 for secondary

        # Monitor configurations
        self.monitors = [
            {"name": "Main Monitor", "resolution": (1360, 768), "scale": 1.0},
            {"name": "Secondary Monitor", "resolution": (1920, 1080), "scale": 1.5},
        ]

        # Event scripts storage
        self.event_scripts = {}
        self.event_schedule = {}

        # Load configuration
        self.load_config()

        # Setup GUI
        self.setup_gui()

        # Setup hotkeys
        self.setup_hotkeys()

        # Timer thread
        self.timer_thread = None
        self.start_timer()

    def setup_gui(self):
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Main Control Tab
        self.setup_main_tab(notebook)

        # Accounts Tab
        self.setup_accounts_tab(notebook)

        # Events Tab
        self.setup_events_tab(notebook)

        # Settings Tab
        self.setup_settings_tab(notebook)

        # Log Tab
        self.setup_log_tab(notebook)

    def setup_main_tab(self, notebook):
        main_frame = ttk.Frame(notebook)
        notebook.add(main_frame, text="Main Control")

        # Status Frame
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding=10)
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.status_label = ttk.Label(
            status_frame, text="Status: Stopped", font=("Arial", 12, "bold")
        )
        self.status_label.pack()

        self.timer_label = ttk.Label(
            status_frame, text="Timer: 00:00:00", font=("Arial", 10)
        )
        self.timer_label.pack()

        self.progress_label = ttk.Label(status_frame, text="Progress: Ready")
        self.progress_label.pack()

        # Control Buttons Frame
        control_frame = ttk.LabelFrame(main_frame, text="Controls", padding=10)
        control_frame.pack(fill=tk.X, padx=5, pady=5)

        button_frame = ttk.Frame(control_frame)
        button_frame.pack()

        self.start_btn = ttk.Button(
            button_frame, text="Start (F1)", command=self.start_bot, width=12
        )
        self.start_btn.grid(row=0, column=0, padx=2)

        self.pause_btn = ttk.Button(
            button_frame, text="Pause (F2)", command=self.pause_bot, width=12
        )
        self.pause_btn.grid(row=0, column=1, padx=2)

        self.next_btn = ttk.Button(
            button_frame, text="Next (F3)", command=self.next_step, width=12
        )
        self.next_btn.grid(row=0, column=2, padx=2)

        self.repeat_btn = ttk.Button(
            button_frame, text="Repeat (F4)", command=self.repeat_step, width=12
        )
        self.repeat_btn.grid(row=0, column=3, padx=2)

        self.stop_btn = ttk.Button(
            button_frame, text="Stop (F5)", command=self.stop_bot, width=12
        )
        self.stop_btn.grid(row=0, column=4, padx=2)

        # Monitor Selection
        monitor_frame = ttk.LabelFrame(main_frame, text="Monitor Selection", padding=10)
        monitor_frame.pack(fill=tk.X, padx=5, pady=5)

        self.monitor_var = tk.IntVar(value=0)
        for i, monitor in enumerate(self.monitors):
            ttk.Radiobutton(
                monitor_frame,
                text=f"{monitor['name']} ({monitor['resolution'][0]}x{monitor['resolution'][1]})",
                variable=self.monitor_var,
                value=i,
            ).pack(anchor=tk.W)

        # Screenshot Frame
        screenshot_frame = ttk.LabelFrame(
            main_frame, text="Screenshot Tools", padding=10
        )
        screenshot_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(
            screenshot_frame, text="Take Screenshot", command=self.take_screenshot
        ).pack(side=tk.LEFT, padx=5)
        ttk.Button(
            screenshot_frame, text="Capture Region", command=self.capture_region
        ).pack(side=tk.LEFT, padx=5)

    def setup_accounts_tab(self, notebook):
        accounts_frame = ttk.Frame(notebook)
        notebook.add(accounts_frame, text="Accounts")

        # Account List Frame
        list_frame = ttk.LabelFrame(accounts_frame, text="Account List", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Treeview for accounts
        columns = ("Account", "Characters", "Status", "Last Login")
        self.account_tree = ttk.Treeview(
            list_frame, columns=columns, show="headings", height=10
        )

        for col in columns:
            self.account_tree.heading(col, text=col)
            self.account_tree.column(col, width=150)

        scrollbar = ttk.Scrollbar(
            list_frame, orient=tk.VERTICAL, command=self.account_tree.yview
        )
        self.account_tree.configure(yscrollcommand=scrollbar.set)

        self.account_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Account Management Frame
        mgmt_frame = ttk.LabelFrame(
            accounts_frame, text="Account Management", padding=10
        )
        mgmt_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(mgmt_frame, text="Add Account", command=self.add_account).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(mgmt_frame, text="Edit Account", command=self.edit_account).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(mgmt_frame, text="Remove Account", command=self.remove_account).pack(
            side=tk.LEFT, padx=5
        )

    def setup_events_tab(self, notebook):
        events_frame = ttk.Frame(notebook)
        notebook.add(events_frame, text="Events")

        # Event Scripts Frame
        scripts_frame = ttk.LabelFrame(events_frame, text="Event Scripts", padding=10)
        scripts_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Event list
        self.event_listbox = tk.Listbox(scripts_frame, height=8)
        self.event_listbox.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Event management buttons
        event_btn_frame = ttk.Frame(scripts_frame)
        event_btn_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(
            event_btn_frame, text="Load Script", command=self.load_event_script
        ).pack(side=tk.LEFT, padx=2)
        ttk.Button(
            event_btn_frame, text="Schedule Event", command=self.schedule_event
        ).pack(side=tk.LEFT, padx=2)
        ttk.Button(
            event_btn_frame, text="Remove Event", command=self.remove_event
        ).pack(side=tk.LEFT, padx=2)

        # Event Schedule Frame
        schedule_frame = ttk.LabelFrame(events_frame, text="Event Schedule", padding=10)
        schedule_frame.pack(fill=tk.X, padx=5, pady=5)

        self.schedule_text = scrolledtext.ScrolledText(schedule_frame, height=6)
        self.schedule_text.pack(fill=tk.BOTH, expand=True)

    def setup_settings_tab(self, notebook):
        settings_frame = ttk.Frame(notebook)
        notebook.add(settings_frame, text="Settings")

        # Delay Settings
        delay_frame = ttk.LabelFrame(settings_frame, text="Delay Settings", padding=10)
        delay_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(delay_frame, text="Min Delay (seconds):").grid(
            row=0, column=0, sticky=tk.W, padx=5
        )
        self.min_delay_var = tk.DoubleVar(value=1.0)
        ttk.Entry(delay_frame, textvariable=self.min_delay_var, width=10).grid(
            row=0, column=1, padx=5
        )

        ttk.Label(delay_frame, text="Max Delay (seconds):").grid(
            row=1, column=0, sticky=tk.W, padx=5
        )
        self.max_delay_var = tk.DoubleVar(value=3.0)
        ttk.Entry(delay_frame, textvariable=self.max_delay_var, width=10).grid(
            row=1, column=1, padx=5
        )

        # Image Recognition Settings
        img_frame = ttk.LabelFrame(settings_frame, text="Image Recognition", padding=10)
        img_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(img_frame, text="Confidence Threshold:").grid(
            row=0, column=0, sticky=tk.W, padx=5
        )
        self.confidence_var = tk.DoubleVar(value=0.8)
        ttk.Entry(img_frame, textvariable=self.confidence_var, width=10).grid(
            row=0, column=1, padx=5
        )

        # Save Settings Button
        ttk.Button(settings_frame, text="Save Settings", command=self.save_config).pack(
            pady=10
        )

    def setup_log_tab(self, notebook):
        log_frame = ttk.Frame(notebook)
        notebook.add(log_frame, text="Logs")

        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Log control buttons
        log_btn_frame = ttk.Frame(log_frame)
        log_btn_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(log_btn_frame, text="Clear Logs", command=self.clear_logs).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(log_btn_frame, text="Save Logs", command=self.save_logs).pack(
            side=tk.LEFT, padx=5
        )

    def setup_hotkeys(self):
        """Setup global hotkeys"""
        try:
            keyboard.add_hotkey("f1", self.start_bot)
            keyboard.add_hotkey("f2", self.pause_bot)
            keyboard.add_hotkey("f3", self.next_step)
            keyboard.add_hotkey("f4", self.repeat_step)
            keyboard.add_hotkey("f5", self.stop_bot)
            self.log("Hotkeys registered successfully")
        except Exception as e:
            self.log(f"Error setting up hotkeys: {str(e)}")

    def log(self, message):
        """Add message to log with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        print(log_message.strip())

    def start_timer(self):
        """Start the timer thread"""
        if self.timer_thread is None or not self.timer_thread.is_alive():
            self.timer_thread = threading.Thread(target=self.update_timer, daemon=True)
            self.timer_thread.start()

    def update_timer(self):
        """Update timer display"""
        while True:
            if self.is_running and self.timer_start:
                elapsed = datetime.now() - self.timer_start
                hours, remainder = divmod(elapsed.total_seconds(), 3600)
                minutes, seconds = divmod(remainder, 60)
                timer_text = (
                    f"Timer: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
                )
                self.timer_label.config(text=timer_text)
            time.sleep(1)

    def find_draconia_window(self):
        """Find DraconiaSaga window on the selected monitor"""
        try:
            self.log("DIAGNOSTIC: Attempting to find DraconiaSaga window...")
            
            # Check for various possible window titles
            possible_titles = ["DraconiaSaga", "Draconia", "Draconia Saga"]
            windows = []
            
            for title in possible_titles:
                found_windows = gw.getWindowsWithTitle(title)
                if found_windows:
                    self.log(f"DIAGNOSTIC: Found {len(found_windows)} window(s) with title '{title}'")
                    windows.extend(found_windows)
                else:
                    self.log(f"DIAGNOSTIC: No windows found with title '{title}'")
            
            if not windows:
                self.log("DIAGNOSTIC: ERROR - No DraconiaSaga windows found with any expected title")
                all_windows = gw.getAllWindows()
                self.log(f"DIAGNOSTIC: Total windows open: {len(all_windows)}")
                game_related = [w for w in all_windows if any(keyword in w.title.lower() for keyword in ['draconia', 'saga', 'game'])]
                if game_related:
                    self.log(f"DIAGNOSTIC: Possible game-related windows: {[w.title for w in game_related]}")
                return None

            # Get the selected monitor info
            monitor = self.monitors[self.monitor_var.get()]
            self.log(f"DIAGNOSTIC: Selected monitor: {monitor['name']} ({monitor['resolution']})")

            # Find window on the selected monitor
            for i, window in enumerate(windows):
                self.log(f"DIAGNOSTIC: Window {i+1}: '{window.title}' - Active: {window.isActive}, Position: ({window.left}, {window.top})")
                if window.isActive or len(windows) == 1:
                    self.log(f"DIAGNOSTIC: Selected window: {window.title}")
                    return window

            self.log(f"DIAGNOSTIC: No active window found, returning first window: {windows[0].title}")
            return windows[0]  # Return first window if none active
        except Exception as e:
            self.log(f"DIAGNOSTIC: ERROR in find_draconia_window: {str(e)}")
            return None

    def take_screenshot(self):
        """Take screenshot of DraconiaSaga window"""
        try:
            window = self.find_draconia_window()
            if not window:
                messagebox.showerror("Error", "DraconiaSaga window not found")
                return

            # Get window bounds
            left, top, right, bottom = (
                window.left,
                window.top,
                window.right,
                window.bottom,
            )

            # Take screenshot
            screenshot = pyautogui.screenshot(
                region=(left, top, right - left, bottom - top)
            )

            # Save screenshot
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            screenshot.save(filename)

            self.log(f"Screenshot saved: {filename}")
            messagebox.showinfo("Success", f"Screenshot saved as {filename}")

        except Exception as e:
            self.log(f"Error taking screenshot: {str(e)}")
            messagebox.showerror("Error", f"Failed to take screenshot: {str(e)}")

    def capture_region(self):
        """Capture a specific region for image recognition"""
        self.log("Click and drag to select region...")
        messagebox.showinfo(
            "Region Capture",
            "Click OK, then click and drag to select the region to capture",
        )

        # This would need additional implementation for region selection
        # For now, just take a full screenshot
        self.take_screenshot()

    def random_delay(self):
        """Add random delay between actions"""
        min_delay = self.min_delay_var.get()
        max_delay = self.max_delay_var.get()
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def click_image(self, image_path, confidence=None):
        """Click on an image if found"""
        if confidence is None:
            confidence = self.confidence_var.get()

        self.log(f"DIAGNOSTIC: Attempting to find and click image: {image_path}")
        self.log(f"DIAGNOSTIC: Using confidence threshold: {confidence}")
        
        # Check if image file exists
        import os
        if not os.path.exists(image_path):
            self.log(f"DIAGNOSTIC: ERROR - Image file does not exist: {image_path}")
            return False
            
        try:
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                center = pyautogui.center(location)
                self.log(f"DIAGNOSTIC: Image found at location: {location}, center: {center}")
                pyautogui.click(center)
                self.log(f"DIAGNOSTIC: Successfully clicked on image: {image_path}")
                return True
            else:
                self.log(f"DIAGNOSTIC: Image not found on screen: {image_path}")
                return False
        except Exception as e:
            self.log(f"DIAGNOSTIC: ERROR clicking image {image_path}: {str(e)}")
            return False

    def start_bot(self):
        """Start the bot"""
        if not self.is_running:
            self.is_running = True
            self.is_paused = False
            self.timer_start = datetime.now()
            self.status_label.config(text="Status: Running")
            self.log("Bot started")

            # Start bot thread
            bot_thread = threading.Thread(target=self.run_bot, daemon=True)
            bot_thread.start()

    def pause_bot(self):
        """Pause/unpause the bot"""
        if self.is_running:
            self.is_paused = not self.is_paused
            status = "Paused" if self.is_paused else "Running"
            self.status_label.config(text=f"Status: {status}")
            self.log(f"Bot {status.lower()}")

    def next_step(self):
        """Force next step"""
        if self.is_running:
            self.current_step += 1
            self.log("Forced next step")

    def repeat_step(self):
        """Repeat current step"""
        if self.is_running:
            self.log("Repeating current step")
            # The step will be repeated in the main loop

    def stop_bot(self):
        """Stop the bot"""
        self.is_running = False
        self.is_paused = False
        self.current_step = 0
        self.status_label.config(text="Status: Stopped")
        self.progress_label.config(text="Progress: Ready")
        self.log("Bot stopped")

    def run_bot(self):
        """Main bot execution loop"""
        try:
            while self.is_running:
                if self.is_paused:
                    time.sleep(1)
                    continue

                # Check if DraconiaSaga window is active
                window = self.find_draconia_window()
                if not window:
                    self.log("DraconiaSaga window not found, waiting...")
                    time.sleep(5)
                    continue

                # Execute daily login steps
                self.execute_daily_steps()

                # Check for event scripts
                self.check_event_scripts()

                # Move to next account/character
                self.next_account_character()

                self.random_delay()

        except Exception as e:
            self.log(f"Bot error: {str(e)}")
            self.stop_bot()

    def execute_daily_steps(self):
        """Execute the daily login and reward collection steps according to PDF specification"""
        self.log("=== Starting DraconiaSaga Daily Login Automation ===")
        
        # Define the 7-step process from the PDF
        steps = [
            ("PART 1: LOGIN", self.step_1_login),
            ("PART 2: AFTER LOGIN", self.step_2_after_login),
            ("STEP 3: FUNCTIONS", self.step_3_functions),
            ("STEP 4: RECHARGE", self.step_4_recharge),
            ("STEP 5: BENEFITS", self.step_5_benefits),
            ("STEP 6: SUMMON", self.step_6_summon),
            ("STEP 7: LOGOUT", self.step_7_logout),
        ]

        if self.current_step >= len(steps):
            self.log("All daily steps completed, cycling to next account/character")
            self.current_step = 0
            return

        step_name, step_function = steps[self.current_step]
        self.progress_label.config(text=f"Progress: {step_name}")
        self.log(f"Starting {step_name}")

        try:
            # Execute the step function
            success = step_function()
            
            if success:
                self.log(f"✓ {step_name} completed successfully")
                self.current_step += 1
            else:
                self.log(f"✗ {step_name} failed, will retry")
                # Don't increment step, will retry on next iteration
                
        except Exception as e:
            self.log(f"Error in {step_name}: {str(e)}")
            # Don't increment step, will retry on next iteration

    def step_1_login(self):
        """PART 1: LOGIN - Handle email/password, server, character selection"""
        self.log("--- PART 1: LOGIN ---")
        
        if not self.accounts:
            self.log("ERROR: No accounts configured. Please add accounts in the Accounts tab.")
            return False
            
        current_account = self.accounts[self.current_account]
        username = current_account.get('username', '')
        password = current_account.get('password', '')
        characters = current_account.get('characters', [])
        
        if not username or not password:
            self.log(f"ERROR: Account {self.current_account + 1} missing username or password")
            return False
            
        self.log(f"Logging in with account: {username}")
        
        # Step 1a: Enter email and password
        if not self.handle_login_screen(username, password):
            return False
            
        self.random_delay()
        
        # Step 1b: Select server (S98 according to PDF)
        if not self.handle_server_selection():
            return False
            
        self.random_delay()
        
        # Step 1c: Select character
        if not self.handle_character_selection(characters):
            return False
            
        self.log("Login process completed")
        return True
        
    def handle_login_screen(self, username, password):
        """Handle the login screen with email and password"""
        self.log("Looking for login screen...")
        
        # Try to find email input field
        if self.click_image("images/email_field.png"):
            self.log("Found email field, entering username")
            pyautogui.write(username)
            self.random_delay()
        else:
            self.log("Could not find email field image")
            
        # Try to find password input field  
        if self.click_image("images/password_field.png"):
            self.log("Found password field, entering password")
            pyautogui.write(password)
            self.random_delay()
        else:
            self.log("Could not find password field image")
            
        # Click login button
        if self.click_image("images/login_button.png"):
            self.log("Clicked login button")
            time.sleep(3)  # Wait for login to process
            return True
        else:
            self.log("Could not find login button image")
            return False
            
    def handle_server_selection(self):
        """Handle server selection (S98)"""
        self.log("Looking for server selection screen...")
        
        # Look for S98 server or just click anywhere to continue as per PDF
        if self.click_image("images/server_s98.png"):
            self.log("Selected S98 server")
        elif self.click_image("images/server_selection.png"):
            self.log("Clicked on server selection area")
        else:
            self.log("Server selection images not found, clicking center of screen")
            # Click center of screen as fallback
            window = self.find_draconia_window()
            if window:
                center_x = window.left + (window.width // 2)
                center_y = window.top + (window.height // 2)
                pyautogui.click(center_x, center_y)
                
        time.sleep(2)  # Wait for server selection to process
        return True
        
    def handle_character_selection(self, characters):
        """Handle character selection"""
        self.log("Looking for character selection screen...")
        
        if not characters:
            self.log("No characters configured for this account")
            return False
            
        current_char_index = self.current_character
        if current_char_index >= len(characters):
            current_char_index = 0
            
        character_name = characters[current_char_index]
        self.log(f"Selecting character: {character_name} (index {current_char_index + 1})")
        
        # Try to click on the specific character slot
        char_image = f"images/character_{current_char_index + 1}.png"
        if self.click_image(char_image):
            self.log(f"Selected character slot {current_char_index + 1}")
        else:
            self.log(f"Character slot image not found: {char_image}")
            
        # Click Start button
        if self.click_image("images/start_button.png"):
            self.log("Clicked Start button")
            time.sleep(5)  # Wait for game to load
            return True
        else:
            self.log("Could not find Start button")
            return False

    def step_2_after_login(self):
        """PART 2: AFTER SUCCESSFUL LOGIN - Handle ads, treasure hunter, inventory"""
        self.log("--- PART 2: AFTER SUCCESSFUL LOGIN ---")
        
        # Step 2a: Close ads
        if not self.close_ads():
            return False
            
        self.random_delay()
        
        # Step 2b: Handle treasure hunter rewards (if appears)
        self.handle_treasure_hunter()
        self.random_delay()
        
        # Step 2c: Handle inventory - put items into furnace
        if not self.handle_inventory():
            return False
            
        self.log("After login tasks completed")
        return True
        
    def close_ads(self):
        """Close advertisement popup"""
        self.log("Looking for advertisement popup...")
        
        # Try to find and click X button on ads
        if self.click_image("images/ad_close_x.png"):
            self.log("Closed advertisement popup")
            time.sleep(1)
            return True
        else:
            self.log("No advertisement popup found or already closed")
            return True  # Not finding ads is not a failure
            
    def handle_treasure_hunter(self):
        """Handle treasure hunter rewards if popup appears"""
        self.log("Checking for treasure hunter popup...")
        
        if self.click_image("images/treasure_hunter_popup.png"):
            self.log("Found treasure hunter popup")
            
            # Scroll down to find Treasure Hunter option
            self.scroll_down()
            self.random_delay()
            
            # Select perfect retrieval with diamond logo
            if self.click_image("images/perfect_retrieval.png"):
                self.log("Selected perfect retrieval")
                self.random_delay()
                
                # Click Quick Normal Retrieval
                if self.click_image("images/quick_normal_retrieval.png"):
                    self.log("Clicked Quick Normal Retrieval")
                    self.random_delay()
                    
            # Close the window
            if self.click_image("images/treasure_hunter_close.png"):
                self.log("Closed treasure hunter window")
        else:
            self.log("No treasure hunter popup found")
            
    def handle_inventory(self):
        """Handle inventory - put items into furnace"""
        self.log("Opening inventory...")
        
        # Click inventory button
        if self.click_image("images/inventory_button.png"):
            self.log("Opened inventory")
            self.random_delay()
            
            # Click "Put into the furnace"
            if self.click_image("images/put_into_furnace.png"):
                self.log("Clicked 'Put into the furnace'")
                self.random_delay()
                
                # Tick "All Quality"
                if self.click_image("images/all_quality_checkbox.png"):
                    self.log("Selected 'All Quality'")
                    self.random_delay()
                    
                # Click "Put"
                if self.click_image("images/put_button.png"):
                    self.log("Clicked 'Put' button")
                    self.random_delay()
                    
                    # Click "Confirm"
                    if self.click_image("images/confirm_button.png"):
                        self.log("Clicked 'Confirm' button")
                        self.random_delay()
                        
            # Close inventory window
            if self.click_image("images/inventory_close_x.png"):
                self.log("Closed inventory window")
                return True
            else:
                self.log("Could not close inventory window")
                return False
        else:
            self.log("Could not open inventory")
            return False

    def step_3_functions(self):
        """STEP 3: FUNCTIONS - Dragon Hunters and Brigade"""
        self.log("--- STEP 3: FUNCTIONS ---")
        
        # Step 3a: Dragon Hunters
        if not self.handle_dragon_hunters():
            return False
            
        self.random_delay()
        
        # Step 3b: Brigade
        if not self.handle_brigade():
            return False
            
        self.log("Functions completed")
        return True
        
    def handle_dragon_hunters(self):
        """Handle Dragon Hunters function"""
        self.log("Handling Dragon Hunters...")
        
        # Click Functions button
        if self.click_image("images/functions_button.png"):
            self.log("Opened Functions menu")
            self.random_delay()
            
            # Select Dragon Hunters
            if self.click_image("images/dragon_hunters.png"):
                self.log("Selected Dragon Hunters")
                self.random_delay()
                
                # Click Claim Now
                if self.click_image("images/claim_now.png"):
                    self.log("Clicked Claim Now for Dragon Hunters")
                    self.random_delay()
                    
                    # Handle USE button popups
                    self.handle_use_button_popups()
                    
                # Close Dragon Hunter window
                if self.click_image("images/dragon_hunters_close.png"):
                    self.log("Closed Dragon Hunters window")
                    return True
                else:
                    self.log("Could not close Dragon Hunters window")
                    return False
        else:
            self.log("Could not open Functions menu")
            return False
            
    def handle_brigade(self):
        """Handle Brigade function"""
        self.log("Handling Brigade...")
        
        # Click Functions button again
        if self.click_image("images/functions_button.png"):
            self.log("Opened Functions menu")
            self.random_delay()
            
            # Select Brigade
            if self.click_image("images/brigade.png"):
                self.log("Selected Brigade")
                self.random_delay()
                
                # Click Claim Now
                if self.click_image("images/brigade_claim_now.png"):
                    self.log("Clicked Claim Now for Brigade")
                    self.random_delay()
                    
                # Click Brigade Support
                if self.click_image("images/brigade_support.png"):
                    self.log("Clicked Brigade Support")
                    self.random_delay()
                    
                    # Click Brigade Support (Green box) again
                    if self.click_image("images/brigade_support_green.png"):
                        self.log("Clicked Brigade Support (Green box)")
                        self.random_delay()
                        
                # Close Brigade window
                if self.click_image("images/brigade_close.png"):
                    self.log("Closed Brigade window")
                    return True
                else:
                    self.log("Could not close Brigade window")
                    return False
        else:
            self.log("Could not open Functions menu")
            return False
            
    def handle_use_button_popups(self):
        """Handle USE button popups that appear after claiming"""
        self.log("Handling USE button popups...")
        
        # Keep clicking USE buttons until they stop appearing
        use_count = 0
        max_attempts = 10  # Prevent infinite loop
        
        while use_count < max_attempts:
            if self.click_image("images/use_button.png"):
                self.log(f"Clicked USE button #{use_count + 1}")
                use_count += 1
                self.random_delay()
            else:
                break
                
        if use_count > 0:
            self.log(f"Handled {use_count} USE button popups")
        else:
            self.log("No USE button popups found")

    def step_4_recharge(self):
        """STEP 4: RECHARGE - Value Pack, Daily Pack, Weekly Pack"""
        self.log("--- STEP 4: RECHARGE ---")
        
        # Click Recharge button
        if self.click_image("images/recharge_button.png"):
            self.log("Opened Recharge menu")
            self.random_delay()
            
            # Handle Value Pack
            if self.click_image("images/value_pack.png"):
                self.log("Selected Value Pack")
                self.random_delay()
                
                if self.click_image("images/value_pack_claim.png"):
                    self.log("Claimed Value Pack")
                    self.random_delay()
                    
            # Handle Daily Pack
            if self.click_image("images/daily_pack.png"):
                self.log("Selected Daily Pack")
                self.random_delay()
                
                if self.click_image("images/daily_pack_claim.png"):
                    self.log("Claimed Daily Pack")
                    self.random_delay()
                    
            # Handle Daily Free Gift
            if self.click_image("images/daily_free_gift.png"):
                self.log("Found Daily Free Gift")
                self.random_delay()
                
                if self.click_image("images/daily_free_gift_claim.png"):
                    self.log("Claimed Daily Free Gift")
                    self.random_delay()
                    
            # Handle Weekly Pack (only available on Monday)
            current_day = datetime.now().strftime("%A")
            if current_day == "Monday":
                self.log("Monday detected - checking Weekly Pack")
                if self.click_image("images/weekly_pack.png"):
                    self.log("Selected Weekly Pack")
                    self.random_delay()
                    
                    if self.click_image("images/weekly_pack_claim.png"):
                        self.log("Claimed Weekly Pack")
                        self.random_delay()
            else:
                self.log(f"Today is {current_day} - Weekly Pack not available")
                
            # Click anywhere to continue
            window = self.find_draconia_window()
            if window:
                center_x = window.left + (window.width // 2)
                center_y = window.top + (window.height // 2)
                pyautogui.click(center_x, center_y)
                self.log("Clicked to continue from Recharge")
                
            return True
        else:
            self.log("Could not open Recharge menu")
            return False

    def step_5_benefits(self):
        """STEP 5: BENEFITS - Sign-in Gift, Golden Egg, Daily Discount, PC Login"""
        self.log("--- STEP 5: BENEFITS ---")
        
        # Click Benefits button
        if self.click_image("images/benefits_button.png"):
            self.log("Opened Benefits menu")
            self.random_delay()
            
            # Handle Sign-in Gift
            self.handle_signin_gift()
            self.random_delay()
            
            # Handle Golden Egg Daily
            self.handle_golden_egg()
            self.random_delay()
            
            # Handle Daily Discount
            self.handle_daily_discount()
            self.random_delay()
            
            # Handle PC Login (need to scroll down)
            self.handle_pc_login()
            self.random_delay()
            
            # Close Benefits window
            if self.click_image("images/benefits_close.png"):
                self.log("Closed Benefits window")
                return True
            else:
                self.log("Could not close Benefits window")
                return False
        else:
            self.log("Could not open Benefits menu")
            return False
            
    def handle_signin_gift(self):
        """Handle Sign-in Gift"""
        self.log("Handling Sign-in Gift...")
        
        # Click on blinking logo for daily sign-in
        if self.click_image("images/signin_daily.png"):
            self.log("Claimed daily sign-in gift")
            self.random_delay()
            
        # Check for 10-day bonus (purple button)
        if self.click_image("images/signin_10day.png"):
            self.log("Claimed 10-day sign-in bonus")
            self.random_delay()
            
        # Check for 20-day bonus (orange button)
        if self.click_image("images/signin_20day.png"):
            self.log("Claimed 20-day sign-in bonus")
            self.random_delay()
            
    def handle_golden_egg(self):
        """Handle Golden Egg Daily"""
        self.log("Handling Golden Egg Daily...")
        
        # Can tap 3 times
        for i in range(3):
            if self.click_image("images/golden_egg_smash.png"):
                self.log(f"Smashed Golden Egg #{i + 1}")
                self.random_delay()
                
                # Click anywhere to continue after each smash
                window = self.find_draconia_window()
                if window:
                    center_x = window.left + (window.width // 2)
                    center_y = window.top + (window.height // 2)
                    pyautogui.click(center_x, center_y)
                    self.random_delay()
            else:
                self.log(f"Golden Egg smash #{i + 1} not available")
                break
                
    def handle_daily_discount(self):
        """Handle Daily Discount"""
        self.log("Handling Daily Discount...")
        
        if self.click_image("images/daily_discount_claim.png"):
            self.log("Claimed Daily Discount")
            self.random_delay()
        else:
            self.log("Daily Discount not available")
            
    def handle_pc_login(self):
        """Handle PC Login rewards"""
        self.log("Handling PC Login rewards...")
        
        # Scroll down to reveal PC Login section
        self.scroll_down()
        self.random_delay()
        
        # Look for PC Login section
        if self.click_image("images/pc_login_section.png"):
            self.log("Found PC Login section")
            self.random_delay()
            
            # Check for available rewards (light up logos)
            reward_count = 0
            max_rewards = 14  # Up to 14 days
            
            for day in range(1, max_rewards + 1):
                reward_image = f"images/pc_login_day_{day}.png"
                if self.click_image(reward_image):
                    self.log(f"Claimed PC Login reward for day {day}")
                    reward_count += 1
                    self.random_delay()
                    
                    # Click anywhere to continue
                    window = self.find_draconia_window()
                    if window:
                        center_x = window.left + (window.width // 2)
                        center_y = window.top + (window.height // 2)
                        pyautogui.click(center_x, center_y)
                        self.random_delay()
                        
            # Check for special box rewards (rabbit holding box)
            if self.click_image("images/pc_login_box.png"):
                self.log("Claimed PC Login box reward")
                reward_count += 1
                self.random_delay()
                
            if reward_count > 0:
                self.log(f"Claimed {reward_count} PC Login rewards")
            else:
                self.log("No PC Login rewards available")
        else:
            self.log("PC Login section not found")

    def step_6_summon(self):
        """STEP 6: SUMMON - Handle multiple summon tabs with free summons"""
        self.log("--- STEP 6: SUMMON ---")
        
        # Click Summon button
        if self.click_image("images/summon_button.png"):
            self.log("Opened Summon menu")
            self.random_delay()
            
            # Close Drakite Rewards popup if it appears
            if self.click_image("images/drakite_rewards_close.png"):
                self.log("Closed Drakite Rewards popup")
                self.random_delay()
                
            # List of summon tabs to check
            summon_tabs = [
                "supreme_monarch",
                "violet_mirage", 
                "lumin_feather",
                "glistening_shadow",
                "tainted_woods",
                "snowy_bloom",
                "holy_summon",
                "summon_dragons"
            ]
            
            # Scroll to reveal hidden tabs
            self.scroll_down()
            self.random_delay()
            
            # Check each tab for free summons
            for tab in summon_tabs:
                self.handle_summon_tab(tab)
                self.random_delay()
                
            # Close Summon window
            if self.click_image("images/summon_close.png"):
                self.log("Closed Summon window")
                return True
            else:
                self.log("Could not close Summon window")
                return False
        else:
            self.log("Could not open Summon menu")
            return False
            
    def handle_summon_tab(self, tab_name):
        """Handle individual summon tab"""
        self.log(f"Checking {tab_name} summon tab...")
        
        # Click on the tab
        tab_image = f"images/summon_{tab_name}.png"
        if self.click_image(tab_image):
            self.log(f"Selected {tab_name} tab")
            self.random_delay()
            
            # Look for "Summon x 1 Free" button
            free_summon_image = f"images/{tab_name}_free_summon.png"
            if self.click_image(free_summon_image):
                self.log(f"Found free summon in {tab_name}")
                self.random_delay()
                
                # Click "Skip All"
                if self.click_image("images/skip_all.png"):
                    self.log("Clicked Skip All")
                    self.random_delay()
                    
                    # Click anywhere to return to main summon page
                    window = self.find_draconia_window()
                    if window:
                        center_x = window.left + (window.width // 2)
                        center_y = window.top + (window.height // 2)
                        pyautogui.click(center_x, center_y)
                        self.random_delay()
            else:
                self.log(f"No free summon available in {tab_name}")
        else:
            self.log(f"Could not find {tab_name} tab")

    def step_7_logout(self):
        """STEP 7: LOGOUT - Change character or account"""
        self.log("--- STEP 7: LOGOUT ---")
        
        # Click Functions button
        if self.click_image("images/functions_button.png"):
            self.log("Opened Functions menu")
            self.random_delay()
            
            # Click Settings (gear icon)
            if self.click_image("images/settings_gear.png"):
                self.log("Opened Settings")
                self.random_delay()
                
                # Click Account
                if self.click_image("images/account_button.png"):
                    self.log("Opened Account menu")
                    self.random_delay()
                    
                    # Determine if we need to change character or account
                    current_account = self.accounts[self.current_account]
                    characters = current_account.get('characters', [])
                    
                    # Check if we have more characters in current account
                    if self.current_character + 1 < len(characters):
                        # Change character
                        if self.click_image("images/change_character.png"):
                            self.log("Selected Change Character")
                            self.random_delay()
                            
                            if self.click_image("images/confirm_button.png"):
                                self.log("Confirmed character change")
                                time.sleep(3)  # Wait for character selection screen
                                return True
                    else:
                        # Change account
                        if self.click_image("images/change_account.png"):
                            self.log("Selected Change Account")
                            self.random_delay()
                            
                            if self.click_image("images/confirm_button.png"):
                                self.log("Confirmed account change")
                                self.random_delay()
                                
                                # Click Switch Account
                                if self.click_image("images/switch_account.png"):
                                    self.log("Clicked Switch Account")
                                    self.random_delay()
                                    
                                    # Close notice
                                    if self.click_image("images/notice_close.png"):
                                        self.log("Closed notice")
                                        time.sleep(3)  # Wait for login screen
                                        return True
                                        
        self.log("Logout process failed")
        return False
        
    def scroll_down(self):
        """Scroll down in the current window"""
        window = self.find_draconia_window()
        if window:
            # Scroll in the center of the window
            center_x = window.left + (window.width // 2)
            center_y = window.top + (window.height // 2)
            pyautogui.scroll(-3, center_x, center_y)  # Negative for scroll down
            self.log("Scrolled down")
        else:
            self.log("Could not scroll - window not found")

    def check_event_scripts(self):
        """Check and execute event scripts if scheduled"""
        current_time = datetime.now()

        for event_name, schedule_info in self.event_schedule.items():
            if self.should_run_event(event_name, schedule_info, current_time):
                self.execute_event_script(event_name)

    def should_run_event(self, event_name, schedule_info, current_time):
        """Check if an event should run based on schedule"""
        # Implement scheduling logic here
        return False  # Placeholder

    def execute_event_script(self, event_name):
        """Execute an event script"""
        if event_name in self.event_scripts:
            try:
                script_path = self.event_scripts[event_name]
                spec = importlib.util.spec_from_file_location(event_name, script_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)

                if hasattr(module, "execute"):
                    module.execute(self)
                    self.log(f"Executed event script: {event_name}")
                else:
                    self.log(f"Event script {event_name} missing execute function")

            except Exception as e:
                self.log(f"Error executing event script {event_name}: {str(e)}")

    def next_account_character(self):
        """Move to next character or account"""
        self.log("DIAGNOSTIC: next_account_character() called")
        
        if not self.accounts:
            self.log("DIAGNOSTIC: No accounts configured - cannot switch")
            return

        current_acc = self.accounts[self.current_account]
        self.log(f"DIAGNOSTIC: Current account: {self.current_account + 1}/{len(self.accounts)} - {current_acc.get('username', 'Unknown')}")
        self.log(f"DIAGNOSTIC: Current character: {self.current_character + 1}/{len(current_acc.get('characters', []))}")

        # Move to next character
        self.current_character += 1
        if self.current_character >= len(current_acc.get("characters", [])):
            self.log("DIAGNOSTIC: Reached end of characters for current account, switching to next account")
            self.current_character = 0
            # Move to next account
            self.current_account += 1
            if self.current_account >= len(self.accounts):
                self.log("DIAGNOSTIC: Reached end of all accounts, cycling back to first account")
                self.current_account = 0
                
        next_acc = self.accounts[self.current_account]
        self.log(f"DIAGNOSTIC: Switched to account: {self.current_account + 1}/{len(self.accounts)} - {next_acc.get('username', 'Unknown')}")
        self.log(f"DIAGNOSTIC: Character: {self.current_character + 1}/{len(next_acc.get('characters', []))}")
        self.log("DIAGNOSTIC: NOTE - Account/character switching logic exists but is NOT connected to actual game logout/login automation")

    def add_account(self):
        """Add new account"""
        dialog = AccountDialog(self.root)
        if dialog.result:
            self.accounts.append(dialog.result)
            self.refresh_account_list()
            self.save_config()

    def edit_account(self):
        """Edit selected account"""
        selection = self.account_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an account to edit")
            return

        # Implementation for editing account
        pass

    def remove_account(self):
        """Remove selected account"""
        selection = self.account_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an account to remove")
            return

        if messagebox.askyesno(
            "Confirm", "Are you sure you want to remove this account?"
        ):
            # Implementation for removing account
            pass

    def refresh_account_list(self):
        """Refresh the account list display"""
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)

        for i, account in enumerate(self.accounts):
            self.account_tree.insert(
                "",
                tk.END,
                values=(
                    account.get("username", f"Account {i + 1}"),
                    len(account.get("characters", [])),
                    account.get("status", "Ready"),
                    account.get("last_login", "Never"),
                ),
            )

    def load_event_script(self):
        """Load an event script"""
        file_path = filedialog.askopenfilename(
            title="Select Event Script",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")],
        )

        if file_path:
            script_name = os.path.basename(file_path).replace(".py", "")
            self.event_scripts[script_name] = file_path
            self.event_listbox.insert(tk.END, script_name)
            self.log(f"Loaded event script: {script_name}")

    def schedule_event(self):
        """Schedule an event"""
        # Implementation for event scheduling dialog
        pass

    def remove_event(self):
        """Remove selected event"""
        selection = self.event_listbox.curselection()
        if selection:
            event_name = self.event_listbox.get(selection[0])
            del self.event_scripts[event_name]
            if event_name in self.event_schedule:
                del self.event_schedule[event_name]
            self.event_listbox.delete(selection[0])
            self.log(f"Removed event: {event_name}")

    def clear_logs(self):
        """Clear the log display"""
        self.log_text.delete(1.0, tk.END)

    def save_logs(self):
        """Save logs to file"""
        file_path = filedialog.asksaveasfilename(
            title="Save Logs",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
        )

        if file_path:
            with open(file_path, "w") as f:
                f.write(self.log_text.get(1.0, tk.END))
            self.log(f"Logs saved to: {file_path}")

    def load_config(self):
        """Load configuration from file"""
        try:
            if os.path.exists("draconia_config.json"):
                with open("draconia_config.json", "r") as f:
                    config = json.load(f)
                    self.accounts = config.get("accounts", [])
                    self.event_scripts = config.get("event_scripts", {})
                    self.event_schedule = config.get("event_schedule", {})

                    # Load settings
                    settings = config.get("settings", {})
                    self.min_delay_var = tk.DoubleVar(
                        value=settings.get("min_delay", 1.0)
                    )
                    self.max_delay_var = tk.DoubleVar(
                        value=settings.get("max_delay", 3.0)
                    )
                    self.confidence_var = tk.DoubleVar(
                        value=settings.get("confidence", 0.8)
                    )

                self.log("Configuration loaded")
        except Exception as e:
            self.log(f"Error loading config: {str(e)}")

    def save_config(self):
        """Save configuration to file"""
        try:
            config = {
                "accounts": self.accounts,
                "event_scripts": self.event_scripts,
                "event_schedule": self.event_schedule,
                "settings": {
                    "min_delay": self.min_delay_var.get(),
                    "max_delay": self.max_delay_var.get(),
                    "confidence": self.confidence_var.get(),
                },
            }

            with open("draconia_config.json", "w") as f:
                json.dump(config, f, indent=2)

            self.log("Configuration saved")
            messagebox.showinfo("Success", "Settings saved successfully")
        except Exception as e:
            self.log(f"Error saving config: {str(e)}")
            messagebox.showerror("Error", f"Failed to save settings: {str(e)}")

    def run(self):
        """Start the GUI application"""
        self.log("DraconiaSaga Daily Login Bot started")
        self.log("Hotkeys: F1=Start, F2=Pause, F3=Next, F4=Repeat, F5=Stop")
        self.root.mainloop()


class AccountDialog:
    def __init__(self, parent):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add Account")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Username
        ttk.Label(self.dialog, text="Username:").grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=5
        )
        self.username_var = tk.StringVar()
        ttk.Entry(self.dialog, textvariable=self.username_var, width=30).grid(
            row=0, column=1, padx=5, pady=5
        )

        # Password
        ttk.Label(self.dialog, text="Password:").grid(
            row=1, column=0, sticky=tk.W, padx=5, pady=5
        )
        self.password_var = tk.StringVar()
        ttk.Entry(self.dialog, textvariable=self.password_var, show="*", width=30).grid(
            row=1, column=1, padx=5, pady=5
        )

        # Characters
        ttk.Label(self.dialog, text="Characters:").grid(
            row=2, column=0, sticky=tk.W, padx=5, pady=5
        )
        self.characters_text = tk.Text(self.dialog, width=30, height=8)
        self.characters_text.grid(row=2, column=1, padx=5, pady=5)

        ttk.Label(self.dialog, text="(One character per line)").grid(
            row=3, column=1, sticky=tk.W, padx=5
        )

        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.grid(row=4, column=0, columnspan=2, pady=10)

        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(
            side=tk.LEFT, padx=5
        )
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(
            side=tk.LEFT, padx=5
        )

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

    def ok_clicked(self):
        """Handle OK button click"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        characters_text = self.characters_text.get(1.0, tk.END).strip()
        
        if not username:
            messagebox.showwarning("Warning", "Please enter a username")
            return
        
        characters = [char.strip() for char in characters_text.split('\n') if char.strip()]
        
        self.result = {
            'username': username,
            'password': password,
            'characters': characters,
            'status': 'Ready',
            'last_login': 'Never'
        }
        
        self.dialog.destroy()

    def cancel_clicked(self):
        """Handle Cancel button click"""
        self.result = None
        self.dialog.destroy()


if __name__ == "__main__":
    bot = DraconiaSagaBot()
    bot.run()
