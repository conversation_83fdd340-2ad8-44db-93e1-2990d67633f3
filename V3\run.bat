@echo off
echo Starting DraconiaSaga Daily Login Bot...
echo =======================================

echo.
echo Checking if Python is available...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please run install.bat first
    pause
    exit /b 1
)

echo.
echo Starting the bot GUI...
python DailyLogin.py

if errorlevel 1 (
    echo.
    echo ERROR: Failed to start the bot
    echo Please check the error messages above
    pause
)
