import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pyautogui
import cv2
import numpy as np
import time
import threading
import json
import os
from datetime import datetime, timedelta
import keyboard
import win32gui
import win32con
from PIL import Image, ImageTk
import random

class DraconiaSagaBot:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("DraconiaSaga Daily Login Bot V4")
        self.root.geometry("1000x700")
        
        # Bot state variables
        self.is_running = False
        self.is_paused = False
        self.current_step = 1
        self.current_account = 0
        self.current_character = 0
        self.timer_start = None
        self.last_action_time = None
        
        # Configuration
        self.config = {
            "accounts": [
                {"email": "<EMAIL>", "password": "Abc123", "server": "S98", "characters": 2},
                {"email": "<EMAIL>", "password": "Abc123", "server": "S98", "characters": 1},
                {"email": "<EMAIL>", "password": "Xyz789", "server": "S98", "characters": 1}
            ],
            "monitor": "main",  # main or secondary
            "screenshot_folder": "screenshots",
            "delays": {"min": 1.0, "max": 3.0},
            "start_step": 1,
            "event_scripts": []
        }
        
        # Create screenshot folders
        os.makedirs("screenshots/main_1360x768", exist_ok=True)
        os.makedirs("screenshots/secondary_1920x1080", exist_ok=True)
        
        # Window detection
        self.game_window = None
        self.monitor_info = {
            "main": {"width": 1360, "height": 768, "scale": 1.0},
            "secondary": {"width": 1920, "height": 1080, "scale": 1.5}
        }
        
        self.setup_gui()
        self.setup_hotkeys()
        self.load_config()
        
    def setup_gui(self):
        # Create main frame with scrollbar
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create canvas and scrollbar
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Control Panel
        control_frame = ttk.LabelFrame(scrollable_frame, text="Control Panel", padding=10)
        control_frame.pack(fill=tk.X, pady=5)
        
        # Timer display
        self.timer_label = ttk.Label(control_frame, text="Timer: 00:00:00", font=("Arial", 12, "bold"))
        self.timer_label.pack(pady=5)
        
        # Status display
        self.status_label = ttk.Label(control_frame, text="Status: Ready", font=("Arial", 10))
        self.status_label.pack(pady=2)
        
        # Control buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(pady=5)
        
        self.start_btn = ttk.Button(button_frame, text="Start (F1)", command=self.start_bot)
        self.start_btn.pack(side=tk.LEFT, padx=2)
        
        self.pause_btn = ttk.Button(button_frame, text="Pause (F2)", command=self.pause_bot)
        self.pause_btn.pack(side=tk.LEFT, padx=2)
        
        self.next_btn = ttk.Button(button_frame, text="Next (F3)", command=self.next_step)
        self.next_btn.pack(side=tk.LEFT, padx=2)
        
        self.repeat_btn = ttk.Button(button_frame, text="Repeat (F4)", command=self.repeat_step)
        self.repeat_btn.pack(side=tk.LEFT, padx=2)
        
        self.stop_btn = ttk.Button(button_frame, text="Stop (F5)", command=self.stop_bot)
        self.stop_btn.pack(side=tk.LEFT, padx=2)
        
        # Monitor Selection
        monitor_frame = ttk.LabelFrame(scrollable_frame, text="Monitor Settings", padding=10)
        monitor_frame.pack(fill=tk.X, pady=5)
        
        self.monitor_var = tk.StringVar(value="main")
        ttk.Radiobutton(monitor_frame, text="Main Monitor (1360x768)", 
                       variable=self.monitor_var, value="main").pack(anchor=tk.W)
        ttk.Radiobutton(monitor_frame, text="Secondary Monitor (1920x1080)", 
                       variable=self.monitor_var, value="secondary").pack(anchor=tk.W)
        
        # Step Selection
        step_frame = ttk.LabelFrame(scrollable_frame, text="Step Selection", padding=10)
        step_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(step_frame, text="Start from step:").pack(anchor=tk.W)
        self.start_step_var = tk.IntVar(value=1)
        step_spinbox = ttk.Spinbox(step_frame, from_=1, to=7, textvariable=self.start_step_var, width=10)
        step_spinbox.pack(anchor=tk.W, pady=2)
        
        # Account Management
        account_frame = ttk.LabelFrame(scrollable_frame, text="Account Management", padding=10)
        account_frame.pack(fill=tk.X, pady=5)
        
        # Account list
        self.account_tree = ttk.Treeview(account_frame, columns=("Email", "Server", "Characters"), show="headings", height=6)
        self.account_tree.heading("Email", text="Email")
        self.account_tree.heading("Server", text="Server")
        self.account_tree.heading("Characters", text="Characters")
        self.account_tree.pack(fill=tk.X, pady=5)
        
        # Account buttons
        acc_btn_frame = ttk.Frame(account_frame)
        acc_btn_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(acc_btn_frame, text="Add Account", command=self.add_account).pack(side=tk.LEFT, padx=2)
        ttk.Button(acc_btn_frame, text="Edit Account", command=self.edit_account).pack(side=tk.LEFT, padx=2)
        ttk.Button(acc_btn_frame, text="Delete Account", command=self.delete_account).pack(side=tk.LEFT, padx=2)
        
        # Screenshot Management
        screenshot_frame = ttk.LabelFrame(scrollable_frame, text="Screenshot Management", padding=10)
        screenshot_frame.pack(fill=tk.X, pady=5)
        
        ttk.Button(screenshot_frame, text="Take Screenshot", command=self.take_screenshot).pack(side=tk.LEFT, padx=2)
        ttk.Button(screenshot_frame, text="Missing Screenshots", command=self.show_missing_screenshots).pack(side=tk.LEFT, padx=2)
        
        # Event Scripts
        event_frame = ttk.LabelFrame(scrollable_frame, text="Event Scripts", padding=10)
        event_frame.pack(fill=tk.X, pady=5)
        
        self.event_listbox = tk.Listbox(event_frame, height=4)
        self.event_listbox.pack(fill=tk.X, pady=2)
        
        event_btn_frame = ttk.Frame(event_frame)
        event_btn_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(event_btn_frame, text="Add Event Script", command=self.add_event_script).pack(side=tk.LEFT, padx=2)
        ttk.Button(event_btn_frame, text="Remove Event Script", command=self.remove_event_script).pack(side=tk.LEFT, padx=2)
        
        # Log Display
        log_frame = ttk.LabelFrame(scrollable_frame, text="Activity Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")
        
        # Update account tree
        self.update_account_tree()
        
        # Start timer update
        self.update_timer()
        
    def setup_hotkeys(self):
        """Setup global hotkeys"""
        try:
            keyboard.add_hotkey('f1', self.start_bot)
            keyboard.add_hotkey('f2', self.pause_bot)
            keyboard.add_hotkey('f3', self.next_step)
            keyboard.add_hotkey('f4', self.repeat_step)
            keyboard.add_hotkey('f5', self.stop_bot)
        except Exception as e:
            self.log(f"Warning: Could not setup hotkeys: {e}")
    
    def log(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        print(log_message.strip())
    
    def update_timer(self):
        """Update timer display"""
        if self.timer_start and self.is_running:
            elapsed = datetime.now() - self.timer_start
            hours, remainder = divmod(elapsed.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            timer_text = f"Timer: {int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
        else:
            timer_text = "Timer: 00:00:00"
        
        self.timer_label.config(text=timer_text)
        self.root.after(1000, self.update_timer)
    
    def find_game_window(self):
        """Find DraconiaSaga game window"""
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                window_title = win32gui.GetWindowText(hwnd)
                if "draconiasaga" in window_title.lower() or "dragonsaga" in window_title.lower():
                    windows.append((hwnd, window_title))
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        if windows:
            self.game_window = windows[0][0]
            self.log(f"Found game window: {windows[0][1]}")
            return True
        else:
            self.log("Game window not found!")
            return False
    
    def get_monitor_screenshot_folder(self):
        """Get screenshot folder based on selected monitor"""
        monitor = self.monitor_var.get()
        if monitor == "main":
            return "screenshots/main_1360x768"
        else:
            return "screenshots/secondary_1920x1080"
    
    def take_screenshot(self):
        """Take screenshot using snipping tool functionality"""
        try:
            # Hide main window temporarily
            self.root.withdraw()
            time.sleep(0.5)
            
            # Create screenshot selection window
            screenshot_window = tk.Toplevel()
            screenshot_window.attributes('-fullscreen', True)
            screenshot_window.attributes('-alpha', 0.3)
            screenshot_window.configure(bg='gray')
            screenshot_window.attributes('-topmost', True)
            
            # Variables for selection
            start_x = start_y = end_x = end_y = 0
            selection_rect = None
            
            def on_mouse_press(event):
                nonlocal start_x, start_y, selection_rect
                start_x, start_y = event.x, event.y
                if selection_rect:
                    canvas.delete(selection_rect)
            
            def on_mouse_drag(event):
                nonlocal selection_rect, end_x, end_y
                end_x, end_y = event.x, event.y
                if selection_rect:
                    canvas.delete(selection_rect)
                selection_rect = canvas.create_rectangle(start_x, start_y, end_x, end_y, outline='red', width=2)
            
            def on_mouse_release(event):
                screenshot_window.destroy()
                self.root.deiconify()
                
                # Take screenshot of selected area
                if abs(end_x - start_x) > 10 and abs(end_y - start_y) > 10:
                    x1, y1 = min(start_x, end_x), min(start_y, end_y)
                    x2, y2 = max(start_x, end_x), max(start_y, end_y)
                    
                    screenshot = pyautogui.screenshot(region=(x1, y1, x2-x1, y2-y1))
                    
                    # Save screenshot
                    folder = self.get_monitor_screenshot_folder()
                    filename = filedialog.asksaveasfilename(
                        initialdir=folder,
                        defaultextension=".png",
                        filetypes=[("PNG files", "*.png"), ("All files", "*.*")]
                    )
                    
                    if filename:
                        screenshot.save(filename)
                        self.log(f"Screenshot saved: {filename}")
            
            canvas = tk.Canvas(screenshot_window, highlightthickness=0)
            canvas.pack(fill=tk.BOTH, expand=True)
            
            canvas.bind('<Button-1>', on_mouse_press)
            canvas.bind('<B1-Motion>', on_mouse_drag)
            canvas.bind('<ButtonRelease-1>', on_mouse_release)
            
            # Instructions
            instruction_label = tk.Label(screenshot_window, text="Click and drag to select area for screenshot. Release to capture.", 
                                       bg='yellow', fg='black', font=('Arial', 12))
            instruction_label.pack(pady=10)
            
        except Exception as e:
            self.root.deiconify()
            self.log(f"Screenshot error: {e}")
    
    def show_missing_screenshots(self):
        """Show list of missing screenshots"""
        required_screenshots = [
            "login_page.png", "email_field.png", "password_field.png", "login_button.png",
            "server_arrow.png", "server_selection.png", "character_1.png", "character_2.png",
            "character_3.png", "character_4.png", "start_button.png", "close_ads_x.png",
            "treasure_hunter.png", "perfect_retrieval.png", "quick_normal_retrieval.png",
            "inventory_button.png", "put_into_furnace.png", "all_quality.png", "put_button.png",
            "confirm_button.png", "functions_button.png", "dragon_hunters.png", "claim_now.png",
            "use_button.png", "brigade.png", "brigade_support.png", "recharge_button.png",
            "value_pack.png", "daily_pack.png", "daily_free_gift.png", "weekly_pack.png",
            "benefits_button.png", "signin_gift.png", "golden_egg.png", "smash_golden_egg.png",
            "daily_discount.png", "pc_login.png", "summon_button.png", "drakite_rewards.png",
            "summon_free.png", "skip_all.png", "settings_gear.png", "account_button.png",
            "change_character.png", "change_account.png", "confirm_button.png", "switch_account.png"
        ]
        
        folder = self.get_monitor_screenshot_folder()
        missing = []
        
        for screenshot in required_screenshots:
            if not os.path.exists(os.path.join(folder, screenshot)):
                missing.append(screenshot)
        
        # Show missing screenshots window
        missing_window = tk.Toplevel(self.root)
        missing_window.title("Missing Screenshots")
        missing_window.geometry("400x500")
        
        ttk.Label(missing_window, text=f"Missing screenshots for {self.monitor_var.get()} monitor:").pack(pady=10)
        
        listbox = tk.Listbox(missing_window, height=20)
        listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        for item in missing:
            listbox.insert(tk.END, item)
        
        if not missing:
            listbox.insert(tk.END, "All screenshots are available!")
    
    def human_delay(self):
        """Add random human-like delay"""
        delay = random.uniform(self.config["delays"]["min"], self.config["delays"]["max"])
        time.sleep(delay)
    
    def find_and_click(self, image_name, confidence=0.8, timeout=10):
        """Find image on screen and click it"""
        folder = self.get_monitor_screenshot_folder()
        image_path = os.path.join(folder, image_name)
        
        if not os.path.exists(image_path):
            self.log(f"Screenshot not found: {image_name}")
            return False
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.is_paused:
                time.sleep(0.5)
                continue
                
            try:
                location = pyautogui.locateOnScreen(image_path, confidence=confidence)
                if location:
                    center = pyautogui.center(location)
                    pyautogui.click(center)
                    self.log(f"Clicked on {image_name} at {center}")
                    self.human_delay()
                    return True
            except Exception as e:
                pass
            
            time.sleep(0.5)
        
        self.log(f"Could not find {image_name}")
        return False
    
    def type_text(self, text):
        """Type text with human-like delays"""
        for char in text:
            pyautogui.typewrite(char)
            time.sleep(random.uniform(0.05, 0.15))
    
    def execute_step_1_login(self):
        """Execute Step 1: Login"""
        self.log("Executing Step 1: Login")
        account = self.config["accounts"][self.current_account]
        
        # Find and click email field
        if not self.find_and_click("email_field.png"):
            return False
        
        # Clear and type email
        pyautogui.hotkey('ctrl', 'a')
        self.type_text(account["email"])
        self.human_delay()
        
        # Find and click password field
        if not self.find_and_click("password_field.png"):
            return False
        
        # Clear and type password
        pyautogui.hotkey('ctrl', 'a')
        self.type_text(account["password"])
        self.human_delay()
        
        # Click login button
        if not self.find_and_click("login_button.png"):
            return False
        
        # Wait for server selection page
        time.sleep(3)
        
        # Click server arrow
        if not self.find_and_click("server_arrow.png"):
            return False
        
        # Select server (assuming S98 for now)
        if not self.find_and_click("server_selection.png"):
            return False
        
        # Click anywhere to continue
        pyautogui.click(500, 400)
        self.human_delay()
        
        # Select character
        character_image = f"character_{self.current_character + 1}.png"
        if not self.find_and_click(character_image):
            # Use fixed positions if image recognition fails
            positions = [(400, 300), (600, 300), (400, 450), (600, 450)]
            if self.current_character < len(positions):
                pos = positions[self.current_character]
                pyautogui.click(pos)
                self.log(f"Clicked character at fixed position {pos}")
                self.human_delay()
        
        # Click start button
        if not self.find_and_click("start_button.png"):
            return False
        
        return True
    
    def execute_step_2_after_login(self):
        """Execute Step 2: After successful login"""
        self.log("Executing Step 2: After successful login")
        
        # Close ads (may be multiple)
        for i in range(5):  # Try up to 5 times
            if self.find_and_click("close_ads_x.png", timeout=2):
                self.human_delay()
            else:
                break
        
        # Check for treasure hunter retrieval
        if self.find_and_click("treasure_hunter.png", timeout=3):
            if self.find_and_click("perfect_retrieval.png"):
                if self.find_and_click("quick_normal_retrieval.png"):
                    self.find_and_click("close_ads_x.png")
        
        # Open inventory
        if not self.find_and_click("inventory_button.png"):
            return False
        
        # Put into furnace
        if self.find_and_click("put_into_furnace.png"):
            if self.find_and_click("all_quality.png"):
                if self.find_and_click("put_button.png"):
                    self.find_and_click("confirm_button.png")
        
        # Close inventory
        self.find_and_click("close_ads_x.png")
        
        return True
    
    def execute_step_3_functions(self):
        """Execute Step 3: Functions"""
        self.log("Executing Step 3: Functions")
        
        # Click Functions
        if not self.find_and_click("functions_button.png"):
            return False
        
        # Dragon Hunters
        if self.find_and_click("dragon_hunters.png"):
            if self.find_and_click("claim_now.png"):
                # Keep clicking USE until it's gone
                for i in range(10):
                    if not self.find_and_click("use_button.png", timeout=2):
                        break
                    self.human_delay()
            self.find_and_click("close_ads_x.png")
        
        # Click Functions again
        if not self.find_and_click("functions_button.png"):
            return False
        
        # Brigade
        if self.find_and_click("brigade.png"):
            if self.find_and_click("claim_now.png"):
                self.find_and_click("brigade_support.png")
                self.find_and_click("brigade_support.png")  # Click twice
            self.find_and_click("close_ads_x.png")
        
        return True
    
    def execute_step_4_recharge(self):
        """Execute Step 4: Recharge"""
        self.log("Executing Step 4: Recharge")
        
        if not self.find_and_click("recharge_button.png"):
            return False
        
        # Value Pack
        if self.find_and_click("value_pack.png"):
            self.find_and_click("claim_now.png")
        
        # Daily Pack
        if self.find_and_click("daily_pack.png"):
            self.find_and_click("claim_now.png")
        
        # Daily Free Gift
        if self.find_and_click("daily_free_gift.png"):
            self.find_and_click("claim_now.png")
        
        # Weekly Pack (only on Mondays)
        if datetime.now().weekday() == 0:  # Monday
            if self.find_and_click("weekly_pack.png"):
                self.find_and_click("claim_now.png")
        
        # Click anywhere to continue
        pyautogui.click(500, 400)
        self.human_delay()
        
        return True
    
    def execute_step_5_benefits(self):
        """Execute Step 5: Benefits"""
        self.log("Executing Step 5: Benefits")
        
        if not self.find_and_click("benefits_button.png"):
            return False
        
        # Sign-in Gift
        self.find_and_click("signin_gift.png")
        
        # Golden Egg Daily (3 times)
        if self.find_and_click("golden_egg.png"):
            for i in range(3):
                if self.find_and_click("smash_golden_egg.png"):
                    pyautogui.click(500, 400)  # Click anywhere to continue
                    self.human_delay()
        
        # Daily Discount
        if self.find_and_click("daily_discount.png"):
            self.find_and_click("claim_now.png")
        
        # PC Login (scroll down first)
        pyautogui.scroll(-3)  # Scroll down
        self.human_delay()
        
        if self.find_and_click("pc_login.png"):
            # Try to claim available rewards
            for i in range(14):
                if not self.find_and_click("pc_login_reward.png", timeout=1):
                    break
                pyautogui.click(500, 400)  # Click anywhere to continue
                self.human_delay()
        
        # Close Benefits
        self.find_and_click("close_ads_x.png")
        
        return True
    
    def execute_step_6_summon(self):
        """Execute Step 6: Summon"""
        self.log("Executing Step 6: Summon")
        
        if not self.find_and_click("summon_button.png"):
            return False
        
        # Close Drakite Rewards if appears
        self.find_and_click("close_ads_x.png", timeout=2)
        
        # List of summon tabs to check
        summon_tabs = [
            "supreme_monarch.png", "violet_mirage.png", "lumin_feather.png",
            "glistening_shadow.png", "tainted_woods.png", "snowy_bloom.png",
            "holy_summon.png", "summon_dragons.png"
        ]
        
        # Scroll to show all tabs
        pyautogui.scroll(-3)
        self.human_delay()
        
        for tab in summon_tabs:
            if self.find_and_click(tab, timeout=2):
                if self.find_and_click("summon_free.png", timeout=2):
                    if self.find_and_click("skip_all.png"):
                        pyautogui.click(500, 400)  # Click away from box
                        self.human_delay()
        
        # Close Summon
        self.find_and_click("close_ads_x.png")
        
        return True
    
    def execute_step_7_logout(self):
        """Execute Step 7: Logout"""
        self.log("Executing Step 7: Logout")
        
        # Click Functions
        if not self.find_and_click("functions_button.png"):
            return False
        
        # Click Settings (gear)
        if not self.find_and_click("settings_gear.png"):
            return False
        
        # Click Account
        if not self.find_and_click("account_button.png"):
            return False
        
        account = self.config["accounts"][self.current_account]
        
        # Determine if we need to change character or account
        if account["characters"] > 1 and self.current_character < account["characters"] - 1:
            # Change character
            if self.find_and_click("change_character.png"):
                if self.find_and_click("confirm_button.png"):
                    self.current_character += 1
                    self.current_step = 1  # Start from step 1 for next character
                    return True
        else:
            # Change account
            if self.find_and_click("change_account.png"):
                if self.find_and_click("confirm_button.png"):
                    if self.find_and_click("switch_account.png"):
                        self.find_and_click("close_ads_x.png")
                        self.current_character = 0
                        self.current_account = (self.current_account + 1) % len(self.config["accounts"])
                        self.current_step = 1  # Start from step 1 for next account
                        return True
        
        return False
    
    def execute_current_step(self):
        """Execute the current step"""
        if not self.find_game_window():
            return False
        
        # Bring game window to front
        win32gui.SetForegroundWindow(self.game_window)
        time.sleep(1)
        
        # Execute event scripts if any (between step 2 and 3)
        if self.current_step == 3 and self.config["event_scripts"]:
            self.execute_event_scripts()
        
        step_functions = {
            1: self.execute_step_1_login,
            2: self.execute_step_2_after_login,
            3: self.execute_step_3_functions,
            4: self.execute_step_4_recharge,
            5: self.execute_step_5_benefits,
            6: self.execute_step_6_summon,
            7: self.execute_step_7_logout
        }
        
        if self.current_step in step_functions:
            self.status_label.config(text=f"Status: Executing Step {self.current_step}")
            success = step_functions[self.current_step]()
            
            if success:
                self.log(f"Step {self.current_step} completed successfully")
                if self.current_step < 7:
                    self.current_step += 1
                else:
                    # Check if we have more characters or accounts
                    account = self.config["accounts"][self.current_account]
                    if self.current_character < account["characters"] - 1 or self.current_account < len(self.config["accounts"]) - 1:
                        self.current_step = 7  # Go to logout step
                    else:
                        self.log("All accounts and characters completed!")
                        self.stop_bot()
                        return True
            else:
                self.log(f"Step {self.current_step} failed")
                self.status_label.config(text=f"Status: Step {self.current_step} failed - Manual intervention needed")
                return False
        
        return True
    
    def execute_event_scripts(self):
        """Execute event scripts between step 2 and 3"""
        self.log("Executing event scripts...")
        for script_path in self.config["event_scripts"]:
            try:
                if os.path.exists(script_path):
                    # Import and execute the event script
                    import importlib.util
                    spec = importlib.util.spec_from_file_location("event_script", script_path)
                    if spec and spec.loader:
                        event_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(event_module)
                        
                        # Call the main function if it exists
                        if hasattr(event_module, 'execute_event'):
                            event_module.execute_event(self)
                            self.log(f"Executed event script: {script_path}")
                        else:
                            self.log(f"Event script {script_path} has no execute_event function")
                else:
                    self.log(f"Event script not found: {script_path}")
            except Exception as e:
                self.log(f"Error executing event script {script_path}: {e}")
    
    def start_bot(self):
        """Start the bot"""
        if not self.is_running:
            self.is_running = True
            self.is_paused = False
            self.timer_start = datetime.now()
            self.current_step = self.start_step_var.get()
            self.config["monitor"] = self.monitor_var.get()
            
            self.log("Bot started")
            self.status_label.config(text="Status: Running")
            
            # Start bot thread
            self.bot_thread = threading.Thread(target=self.bot_loop, daemon=True)
            self.bot_thread.start()
    
    def pause_bot(self):
        """Pause/unpause the bot"""
        if self.is_running:
            self.is_paused = not self.is_paused
            if self.is_paused:
                self.log("Bot paused")
                self.status_label.config(text="Status: Paused")
            else:
                self.log("Bot resumed")
                self.status_label.config(text="Status: Running")
    
    def next_step(self):
        """Skip to next step"""
        if self.is_running:
            self.current_step = min(self.current_step + 1, 7)
            self.log(f"Skipped to step {self.current_step}")
    
    def repeat_step(self):
        """Repeat current step"""
        if self.is_running:
            self.log(f"Repeating step {self.current_step}")
    
    def stop_bot(self):
        """Stop the bot"""
        self.is_running = False
        self.is_paused = False
        self.timer_start = None
        self.log("Bot stopped")
        self.status_label.config(text="Status: Stopped")
    
    def bot_loop(self):
        """Main bot loop"""
        while self.is_running:
            if not self.is_paused:
                try:
                    self.execute_current_step()
                    time.sleep(2)  # Wait between steps
                except Exception as e:
                    self.log(f"Bot error: {e}")
                    self.status_label.config(text="Status: Error - Check log")
                    time.sleep(5)
            else:
                time.sleep(1)
    
    def load_config(self):
        """Load configuration from file"""
        config_file = "config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                self.log("Configuration loaded")
            except Exception as e:
                self.log(f"Error loading config: {e}")
    
    def save_config(self):
        """Save configuration to file"""
        try:
            with open("config.json", 'w') as f:
                json.dump(self.config, f, indent=2)
            self.log("Configuration saved")
        except Exception as e:
            self.log(f"Error saving config: {e}")
    
    def update_account_tree(self):
        """Update the account tree display"""
        # Clear existing items
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)
        
        # Add accounts
        for i, account in enumerate(self.config["accounts"]):
            self.account_tree.insert("", "end", values=(
                account["email"], 
                account["server"], 
                account["characters"]
            ))
    
    def add_account(self):
        """Add new account"""
        dialog = AccountDialog(self.root, "Add Account")
        if dialog.result:
            self.config["accounts"].append(dialog.result)
            self.update_account_tree()
            self.save_config()
    
    def edit_account(self):
        """Edit selected account"""
        selection = self.account_tree.selection()
        if selection:
            index = self.account_tree.index(selection[0])
            account = self.config["accounts"][index]
            
            dialog = AccountDialog(self.root, "Edit Account", account)
            if dialog.result:
                self.config["accounts"][index] = dialog.result
                self.update_account_tree()
                self.save_config()
    
    def delete_account(self):
        """Delete selected account"""
        selection = self.account_tree.selection()
        if selection:
            if messagebox.askyesno("Confirm", "Delete selected account?"):
                index = self.account_tree.index(selection[0])
                del self.config["accounts"][index]
                self.update_account_tree()
                self.save_config()
    
    def add_event_script(self):
        """Add event script"""
        filename = filedialog.askopenfilename(
            title="Select Event Script",
            filetypes=[("Python files", "*.py"), ("All files", "*.*")]
        )
        if filename:
            self.config["event_scripts"].append(filename)
            self.event_listbox.insert(tk.END, os.path.basename(filename))
            self.save_config()
    
    def remove_event_script(self):
        """Remove selected event script"""
        selection = self.event_listbox.curselection()
        if selection:
            index = selection[0]
            del self.config["event_scripts"][index]
            self.event_listbox.delete(index)
            self.save_config()
    
    def run(self):
        """Start the GUI"""
        self.root.mainloop()


class AccountDialog:
    def __init__(self, parent, title, account=None):
        self.result = None
        
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("300x200")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))
        
        # Email
        ttk.Label(self.dialog, text="Email:").pack(pady=5)
        self.email_var = tk.StringVar(value=account["email"] if account else "")
        ttk.Entry(self.dialog, textvariable=self.email_var, width=30).pack(pady=2)
        
        # Password
        ttk.Label(self.dialog, text="Password:").pack(pady=5)
        self.password_var = tk.StringVar(value=account["password"] if account else "")
        ttk.Entry(self.dialog, textvariable=self.password_var, width=30, show="*").pack(pady=2)
        
        # Server
        ttk.Label(self.dialog, text="Server:").pack(pady=5)
        self.server_var = tk.StringVar(value=account["server"] if account else "S98")
        ttk.Entry(self.dialog, textvariable=self.server_var, width=30).pack(pady=2)
        
        # Characters
        ttk.Label(self.dialog, text="Number of Characters:").pack(pady=5)
        self.characters_var = tk.IntVar(value=account["characters"] if account else 1)
        ttk.Spinbox(self.dialog, from_=1, to=4, textvariable=self.characters_var, width=28).pack(pady=2)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(pady=10)
        
        ttk.Button(button_frame, text="OK", command=self.ok_clicked).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.LEFT, padx=5)
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def ok_clicked(self):
        self.result = {
            "email": self.email_var.get(),
            "password": self.password_var.get(),
            "server": self.server_var.get(),
            "characters": self.characters_var.get()
        }
        self.dialog.destroy()
    
    def cancel_clicked(self):
        self.dialog.destroy()


if __name__ == "__main__":
    # Create bot instance and run
    bot = DraconiaSagaBot()
    bot.run()
