@echo off
echo DraconiaSaga Daily Login Bot - Installation Script
echo ================================================

echo.
echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found!
python --version

echo.
echo Upgrading pip to latest version...
python -m pip install --upgrade pip

echo.
echo Installing required packages...
echo Note: For Python 3.13+, some packages may need to be compiled from source
echo This may take a few minutes...

pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo WARNING: Some packages failed to install with pip
    echo Trying alternative installation methods...
    echo.
    
    echo Installing packages individually...
    pip install keyboard==0.13.5
    pip install pygetwindow==0.0.9
    
    echo Installing PyAutoGUI...
    pip install pyautogui==0.9.54
    
    echo Installing Pillow (may take time for Python 3.13+)...
    pip install --upgrade <PERSON>llow
    
    if errorlevel 1 (
        echo.
        echo ERROR: Critical packages failed to install
        echo.
        echo For Python 3.13.5, try these solutions:
        echo 1. Use Python 3.11 or 3.12 instead (recommended)
        echo 2. Install Visual Studio Build Tools for compilation
        echo 3. Use conda instead of pip: conda install pillow pyautogui
        echo.
        pause
        exit /b 1
    )
)

echo.
echo Creating necessary directories...
if not exist "images" mkdir images
if not exist "screenshots" mkdir screenshots

echo.
echo Verifying installation...
python -c "import pyautogui, keyboard, pygetwindow, PIL; print('All packages imported successfully!')"

if errorlevel 1 (
    echo.
    echo WARNING: Some packages may not be working correctly
    echo Please check the error messages above
    echo.
)

echo.
echo Installation completed!
echo.
echo IMPORTANT NOTES:
echo - If using Python 3.13+, consider downgrading to Python 3.11 or 3.12
echo - Make sure to capture reference images in the images/ folder
echo - Add your accounts in the bot's Accounts tab before starting
echo.
echo To run the bot:
echo   python DailyLogin.py
echo.
echo Or double-click on run.bat
echo.
pause
